rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // User profile images - users can only access their own
    match /users/{userId}/profile/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if true; // Allow public reading of profile images
    }
    
    // User uploaded content (reviews, etc.)
    match /users/{userId}/uploads/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if true; // Allow public reading
    }
    
    // Public content (destinations, blog images, etc.)
    match /public/{allPaths=**} {
      allow read: if true; // Anyone can read public content
      allow write: if request.auth != null && 
        request.auth.token.admin == true; // Only admins can upload public content
    }
    
    // CMS content from Strapi
    match /cms/{allPaths=**} {
      allow read: if true; // Public reading
      allow write: if request.auth != null && 
        request.auth.token.admin == true; // Admin only
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Auto-delete after 24 hours (handled by Cloud Functions)
    }
  }
}
