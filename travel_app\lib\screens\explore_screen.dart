import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../widgets/destination_card.dart';
import '../models/destination.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  State<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final List<Destination> allDestinations = [
    Destination(
      id: '1',
      name: 'Bali, Indonesia',
      country: 'Indonesia',
      imageUrl: 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=800',
      rating: 4.8,
      price: 299,
      description: 'Tropical paradise with stunning beaches',
    ),
    Destination(
      id: '2',
      name: 'Paris, France',
      country: 'France',
      imageUrl: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=800',
      rating: 4.9,
      price: 599,
      description: 'City of love with iconic landmarks',
    ),
    Destination(
      id: '3',
      name: 'Tokyo, Japan',
      country: 'Japan',
      imageUrl: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800',
      rating: 4.7,
      price: 799,
      description: 'Modern metropolis blending tradition',
    ),
    Destination(
      id: '4',
      name: 'Santorini, Greece',
      country: 'Greece',
      imageUrl: 'https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff?w=800',
      rating: 4.9,
      price: 450,
      description: 'Beautiful island with white buildings',
    ),
    Destination(
      id: '5',
      name: 'New York, USA',
      country: 'United States',
      imageUrl: 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?w=800',
      rating: 4.6,
      price: 699,
      description: 'The city that never sleeps',
    ),
    Destination(
      id: '6',
      name: 'Dubai, UAE',
      country: 'United Arab Emirates',
      imageUrl: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=800',
      rating: 4.8,
      price: 899,
      description: 'Luxury and modern architecture',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // App Bar
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              elevation: 0,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'Explore',
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
              ),
            ),
            
            // Search and Filter Section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: 'Search destinations...',
                          hintStyle: TextStyle(color: Colors.grey[500]),
                          prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
                          suffixIcon: Container(
                            margin: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2E7D8A),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.tune,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Filter Chips
                    Row(
                      children: [
                        Text(
                          'Popular Filters:',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                _buildFilterChip('Beach'),
                                const SizedBox(width: 8),
                                _buildFilterChip('City'),
                                const SizedBox(width: 8),
                                _buildFilterChip('Adventure'),
                                const SizedBox(width: 8),
                                _buildFilterChip('Culture'),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Destinations Grid
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              sliver: SliverMasonryGrid.count(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childCount: allDestinations.length,
                itemBuilder: (context, index) {
                  return DestinationCard(
                    destination: allDestinations[index],
                    width: double.infinity,
                    height: index % 3 == 0 ? 300 : 250,
                  );
                },
              ),
            ),
            
            const SliverToBoxAdapter(
              child: SizedBox(height: 20),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFF2E7D8A).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF2E7D8A).withOpacity(0.3),
        ),
      ),
      child: Text(
        label,
        style: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF2E7D8A),
        ),
      ),
    );
  }
}
