{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401102509969273", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 50322}, "server": "https://developers.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401113028140796", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 40705}, "server": "https://securetoken.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://tile.openstreetmap.org", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 77132}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 57055}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 75771}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 71101}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 64797}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 56953}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://images.unsplash.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401116260814914", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 39458}, "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401116279336711", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 56536}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401116695628717", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 67602}, "server": "https://identitytoolkit.googleapis.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401116756161589", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 59488}, "server": "https://firestore.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401117056441003", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 51287}, "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 54448}, "server": "https://www.google.com"}], "supports_quic": {"address": "***************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G", "CAESABiAgICA+P////8B": "4G"}}}