{"collectionName": "components_food_features", "info": {"displayName": "Restaurant Feature", "description": "Restaurant features and services"}, "options": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "icon": {"type": "string"}, "category": {"type": "enumeration", "enum": ["Dining Style", "Service", "Atmosphere", "Special Offers", "Accessibility", "Entertainment", "Payment", "General"], "default": "General"}, "available": {"type": "boolean", "default": true}}}