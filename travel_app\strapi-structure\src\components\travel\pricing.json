{"collectionName": "components_travel_pricing", "info": {"displayName": "Pricing", "description": "Pricing information for travel packages"}, "options": {}, "attributes": {"basePrice": {"type": "decimal", "required": true, "min": 0}, "currency": {"type": "enumeration", "enum": ["EUR", "USD", "ALL"], "default": "EUR"}, "priceType": {"type": "enumeration", "enum": ["per_person", "per_group", "per_day", "total"], "default": "per_person"}, "discountPrice": {"type": "decimal", "min": 0}, "discountPercentage": {"type": "integer", "min": 0, "max": 100}, "seasonalPricing": {"type": "component", "repeatable": true, "component": "travel.seasonal-price"}, "groupDiscounts": {"type": "component", "repeatable": true, "component": "travel.group-discount"}, "earlyBirdDiscount": {"type": "component", "repeatable": false, "component": "travel.early-bird"}, "lastMinuteDiscount": {"type": "component", "repeatable": false, "component": "travel.last-minute"}, "priceIncludes": {"type": "component", "repeatable": true, "component": "content.list-item"}, "priceExcludes": {"type": "component", "repeatable": true, "component": "content.list-item"}, "paymentTerms": {"type": "richtext"}, "refundPolicy": {"type": "richtext"}}}