// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCcAd6cKQ3j6okmx0KatW68EjUzwNwC0zM',
    appId: '1:797293332911:web:987f026331e86d3783c75a',
    messagingSenderId: '797293332911',
    projectId: 'upalb-travel-app',
    authDomain: 'upalb-travel-app.firebaseapp.com',
    storageBucket: 'upalb-travel-app.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCcAd6cKQ3j6okmx0KatW68EjUzwNwC0zM',
    appId: '1:797293332911:android:987f026331e86d3783c75a',
    messagingSenderId: '797293332911',
    projectId: 'upalb-travel-app',
    storageBucket: 'upalb-travel-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCcAd6cKQ3j6okmx0KatW68EjUzwNwC0zM',
    appId: '1:797293332911:ios:987f026331e86d3783c75a',
    messagingSenderId: '797293332911',
    projectId: 'upalb-travel-app',
    storageBucket: 'upalb-travel-app.firebasestorage.app',
    iosBundleId: 'com.upalb.travel',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCcAd6cKQ3j6okmx0KatW68EjUzwNwC0zM',
    appId: '1:797293332911:ios:987f026331e86d3783c75a',
    messagingSenderId: '797293332911',
    projectId: 'upalb-travel-app',
    storageBucket: 'upalb-travel-app.firebasestorage.app',
    iosBundleId: 'com.upalb.travel',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCcAd6cKQ3j6okmx0KatW68EjUzwNwC0zM',
    appId: '1:797293332911:web:987f026331e86d3783c75a',
    messagingSenderId: '797293332911',
    projectId: 'upalb-travel-app',
    authDomain: 'upalb-travel-app.firebaseapp.com',
    storageBucket: 'upalb-travel-app.firebasestorage.app',
  );
}
