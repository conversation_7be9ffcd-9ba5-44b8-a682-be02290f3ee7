{"collectionName": "components_travel_accommodations", "info": {"displayName": "Accommodation", "description": "Accommodation options and details"}, "options": {}, "attributes": {"type": {"type": "enumeration", "enum": ["Hotel", "Guesthouse", "Hostel", "Apartment", "Villa", "Resort", "Camping", "Traditional House"], "required": true}, "name": {"type": "string"}, "description": {"type": "text"}, "priceRange": {"type": "enumeration", "enum": ["Budget", "Mid-range", "Luxury"], "default": "Mid-range"}, "rating": {"type": "decimal", "min": 0, "max": 5}, "amenities": {"type": "json"}, "bookingUrl": {"type": "string"}, "recommended": {"type": "boolean", "default": false}}}