import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  const String baseUrl = 'http://localhost:1337';
  const String apiToken = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';

  final headers = {
    'Authorization': 'Bearer $apiToken',
    'Content-Type': 'application/json',
  };

  print('Testing Strapi connection...');
  
  try {
    // Test destinations
    print('\n--- Testing Destinations ---');
    final destResponse = await http.get(
      Uri.parse('$baseUrl/api/destinations'),
      headers: headers,
    );
    print('Destinations Status: ${destResponse.statusCode}');
    print('Destinations Response: ${destResponse.body}');

    // Test hotels
    print('\n--- Testing Hotels ---');
    final hotelResponse = await http.get(
      Uri.parse('$baseUrl/api/hotels'),
      headers: headers,
    );
    print('Hotels Status: ${hotelResponse.statusCode}');
    print('Hotels Response: ${hotelResponse.body}');

    // Test travel tips
    print('\n--- Testing Travel Tips ---');
    final tipsResponse = await http.get(
      Uri.parse('$baseUrl/api/travel-tips'),
      headers: headers,
    );
    print('Travel Tips Status: ${tipsResponse.statusCode}');
    print('Travel Tips Response: ${tipsResponse.body}');

    if (destResponse.statusCode == 200) {
      final data = json.decode(destResponse.body);
      final destinations = data['data'] as List;
      print('\n✅ Connection successful!');
      print('Found ${destinations.length} destinations');

      for (var dest in destinations) {
        final attributes = dest['attributes'];
        print('- ${attributes['name']} (${attributes['region']})');
      }
    } else {
      print('❌ Connection failed with status: ${destResponse.statusCode}');
    }
  } catch (e) {
    print('❌ Error: $e');
  }
}
