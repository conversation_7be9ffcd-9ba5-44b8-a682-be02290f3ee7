import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'firebase_options.dart';
import 'screens/main_navigation.dart';
import 'screens/auth/login_screen.dart';
import 'services/app_service.dart';
import 'models/user_model.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase with options
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialized successfully!');

    // Create app service but don't wait for initialization
    final appService = AppService();
    // Add debug listener to user stream
    appService.userStream.listen((user) {
      print('🔊 Main - UserStream emitted: ${user?.name ?? "null"}');
    });
    // Initialize in background - don't await
    appService.initialize();

    runApp(UpAlbaniaApp(appService: appService));
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
    // Run app without Firebase for now
    runApp(const UpAlbaniaApp(appService: null));
  }
}

class UpAlbaniaApp extends StatelessWidget {
  final AppService? appService;

  const UpAlbaniaApp({super.key, required this.appService});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'UpAlbania - Discover Albania',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF00CFC8),
          brightness: Brightness.light,
        ),
        textTheme: GoogleFonts.poppinsTextTheme(),
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.transparent,
          elevation: 0,
          titleTextStyle: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF00CFC8),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
      ),
      home: appService != null
        ? AuthWrapper(appService: appService!)
        : LoginScreen(appService: AppService()), // Fallback without Firebase
    );
  }
}

class AuthWrapper extends StatefulWidget {
  final AppService appService;

  const AuthWrapper({super.key, required this.appService});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UserModel?>(
      stream: widget.appService.userStream,
      initialData: widget.appService.currentUser, // Use current user as initial data
      builder: (context, snapshot) {
        // Check if user is authenticated (from stream or current user)
        final user = snapshot.data ?? widget.appService.currentUser;
        print('🔍 AuthWrapper StreamBuilder - hasData: ${snapshot.hasData}, user: ${user?.name ?? "null"}');
        if (user != null) {
          print('✅ User authenticated: ${user.name} (${user.email})');
          return MainNavigation(appService: widget.appService);
        } else {
          print('🔐 No user authenticated, showing login screen');
          return LoginScreen(appService: widget.appService);
        }
      },
    );
  }
}


