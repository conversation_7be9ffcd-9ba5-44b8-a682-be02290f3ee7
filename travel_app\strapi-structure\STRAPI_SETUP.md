# 🇦🇱 UpAlbania Strapi CMS Setup Guide

## 📋 Overview
This guide will help you set up the complete Strapi CMS structure for the UpAlbania travel app with all content types, components, and sample data.

## 🚀 Quick Setup

### 1. Create Strapi Project
```bash
# Create new Strapi project
npx create-strapi-app@latest upalb-cms --quickstart

# Navigate to project directory
cd upalb-cms

# Install additional dependencies
npm install @strapi/plugin-i18n @strapi/plugin-seo
```

### 2. Copy Structure Files
```bash
# Copy the entire strapi-structure folder contents to your Strapi project
cp -r ../travel_app/strapi-structure/* ./

# Install dependencies
npm install
```

### 3. Start Development Server
```bash
# Start Strapi in development mode
npm run develop

# Access admin panel at http://localhost:1337/admin
```

## 📁 Content Types Structure

### 🏛️ **Destinations**
Complete destination management with:
- **Basic Info**: Title, slug, description, excerpt
- **Location Data**: GPS coordinates, address, region
- **Media**: Featured image, photo gallery
- **Travel Info**: Difficulty, best time to visit, highlights
- **Relationships**: Categories, tags, related destinations
- **SEO**: Meta tags, structured data

### 📝 **Blog Posts**
Rich content management with:
- **Content**: Title, excerpt, rich text content
- **Media**: Featured image, gallery
- **Organization**: Categories, tags, author
- **Engagement**: Read time, view count, likes
- **Relationships**: Related destinations and posts
- **SEO**: Complete meta data

### 🗺️ **Itineraries**
Comprehensive travel packages with:
- **Package Info**: Title, description, duration, difficulty
- **Pricing**: Base price, discounts, seasonal pricing
- **Daily Plans**: Day-by-day itinerary with activities
- **Inclusions**: What's included/excluded
- **Logistics**: Transportation, accommodation
- **Booking**: Availability, cancellation policy

### 🏷️ **Categories & Tags**
Flexible content organization:
- **Categories**: Main content groupings
- **Tags**: Detailed content labeling
- **Color Coding**: Visual organization
- **Relationships**: Connected to all content types

### ✍️ **Authors**
Content creator profiles:
- **Profile**: Name, bio, avatar
- **Contact**: Email, social media
- **Content**: Related posts and articles

## 🧩 Components Structure

### 📍 **Location Components**
- **Coordinates**: GPS data, address, elevation
- **Weather**: Climate information
- **Transportation**: Access methods

### 🎯 **Travel Components**
- **Pricing**: Complex pricing structures
- **Daily Plans**: Detailed itinerary planning
- **Activities**: Activity descriptions and timing
- **Accommodation**: Lodging information
- **Group Size**: Capacity and requirements

### 📄 **Content Components**
- **Highlights**: Key features and attractions
- **List Items**: Organized information lists
- **Tips**: Travel advice and recommendations
- **SEO**: Search engine optimization

## 🌍 Multi-language Support

### Enabled Languages:
- **English** (default)
- **Albanian** (sq)

### Localized Fields:
- All text content (title, description, content)
- SEO metadata
- Rich text fields
- Categories and tags

## 📊 Sample Data

### Pre-loaded Content:
- **3 Destinations**: Saranda, Theth, Berat
- **3 Blog Posts**: Hidden gems, food guide, hiking guide
- **2 Itineraries**: Riviera explorer, Alps adventure
- **5 Categories**: Destinations, culture, adventure, food, tips
- **15 Tags**: Beach, mountains, history, culture, etc.
- **2 Authors**: Elena Hoxha, Marko Gjergji

### Load Sample Data:
```bash
# Run the seed script
node scripts/seed-data.js

# This creates JSON files in scripts/seed-data/
# Import manually through Strapi admin panel
```

## 🔧 Configuration

### Database Configuration
```javascript
// config/database.js
module.exports = ({ env }) => ({
  connection: {
    client: 'postgres', // or 'sqlite' for development
    connection: {
      host: env('DATABASE_HOST', 'localhost'),
      port: env.int('DATABASE_PORT', 5432),
      database: env('DATABASE_NAME', 'upalb_cms'),
      user: env('DATABASE_USERNAME', 'postgres'),
      password: env('DATABASE_PASSWORD', 'password'),
    },
  },
});
```

### Environment Variables
```bash
# .env file
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=upalb_cms
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your_password

# For production
DATABASE_URL=postgresql://user:pass@host:port/db

# Strapi settings
HOST=0.0.0.0
PORT=1337
APP_KEYS=your_app_keys
API_TOKEN_SALT=your_api_token_salt
ADMIN_JWT_SECRET=your_admin_jwt_secret
JWT_SECRET=your_jwt_secret
```

## 🔐 API Configuration

### Public API Endpoints
```javascript
// config/api.js
module.exports = {
  rest: {
    defaultLimit: 25,
    maxLimit: 100,
  },
  responses: {
    privateAttributes: ['created_by', 'updated_by'],
  },
};
```

### CORS Settings
```javascript
// config/middlewares.js
module.exports = [
  'strapi::errors',
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      headers: '*',
      origin: ['http://localhost:3000', 'https://your-app-domain.com']
    }
  },
  // ... other middlewares
];
```

## 📱 Flutter Integration

### API Endpoints
```dart
// Base URL
const String baseUrl = 'http://localhost:1337/api';

// Endpoints
GET /api/destinations?populate=*
GET /api/blog-posts?populate=*
GET /api/itineraries?populate=*
GET /api/categories
GET /api/tags

// Filtering
GET /api/destinations?filters[region][$eq]=Southern Albania
GET /api/blog-posts?filters[category][name][$eq]=Adventure

// Search
GET /api/destinations?filters[$or][0][title][$containsi]=saranda
```

### Sample Flutter Usage
```dart
// Fetch destinations
final response = await http.get(
  Uri.parse('$baseUrl/destinations?populate=*'),
  headers: {'Content-Type': 'application/json'},
);

if (response.statusCode == 200) {
  final data = json.decode(response.body);
  final destinations = (data['data'] as List)
      .map((item) => DestinationPost.fromStrapi(item))
      .toList();
}
```

## 🚀 Deployment

### Heroku Deployment
```bash
# Install Heroku CLI
# Login to Heroku
heroku login

# Create app
heroku create upalb-cms

# Add PostgreSQL
heroku addons:create heroku-postgresql:hobby-dev

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set DATABASE_CLIENT=postgres

# Deploy
git push heroku main
```

### DigitalOcean Deployment
```bash
# Create droplet with Node.js
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Clone and setup
git clone your-repo
cd upalb-cms
npm install
npm run build

# Start with PM2
npm install -g pm2
pm2 start npm --name "upalb-cms" -- start
```

## 🔍 Admin Panel Features

### Content Management
- **Rich Text Editor**: Full WYSIWYG editing
- **Media Library**: Image and video management
- **Bulk Operations**: Mass content updates
- **Draft/Publish**: Content workflow
- **Versioning**: Content history tracking

### User Management
- **Role-Based Access**: Admin, Editor, Author roles
- **Permissions**: Granular content permissions
- **API Tokens**: Secure API access

### Analytics & Monitoring
- **Content Analytics**: View counts, engagement
- **API Usage**: Request monitoring
- **Performance**: Response time tracking

## 🎯 Best Practices

### Content Organization
1. **Use Categories** for main content groupings
2. **Use Tags** for detailed content labeling
3. **SEO Optimization** for all public content
4. **Image Optimization** for faster loading
5. **Regular Backups** of content and media

### Performance
1. **Enable Caching** for API responses
2. **Optimize Images** before upload
3. **Use CDN** for media delivery
4. **Database Indexing** for search performance

### Security
1. **Regular Updates** of Strapi and plugins
2. **Strong Passwords** for admin accounts
3. **API Rate Limiting** to prevent abuse
4. **HTTPS** for production deployment

## 📞 Support & Resources

- **Strapi Documentation**: [strapi.io/documentation](https://strapi.io/documentation)
- **Community Forum**: [forum.strapi.io](https://forum.strapi.io)
- **GitHub Issues**: Report bugs and feature requests
- **Discord Community**: Real-time help and discussion

Your UpAlbania CMS is now ready to power your travel app! 🇦🇱✨
