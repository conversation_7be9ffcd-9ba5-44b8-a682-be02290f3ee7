import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/cms_model.dart';

// Only import sqflite for non-web platforms
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;
  bool _isInitialized = false;

  Future<Database?> get database async {
    // For web platform, always return null
    if (kIsWeb) {
      return null;
    }

    if (!_isInitialized) {
      await _initializePlatform();
    }

    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Helper method to check if database operations should be skipped
  bool get _shouldSkipDatabase => kIsWeb;

  Future<void> _initializePlatform() async {
    if (_isInitialized) return;

    if (kIsWeb) {
      // For web, we'll use in-memory storage instead of SQLite
      print('🌐 Web platform detected - using in-memory storage');
    } else {
      // For mobile platforms, use SQLite
      print('📱 Mobile platform detected - using SQLite');
    }

    _isInitialized = true;
  }

  Future<Database> _initDatabase() async {
    if (kIsWeb) {
      throw UnsupportedError('SQLite not supported on web platform');
    }

    String path = join(await getDatabasesPath(), 'upalb_local.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Users table (cached from Firebase)
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        profile_image TEXT,
        preferences TEXT,
        travel_stats TEXT,
        created_at INTEGER,
        updated_at INTEGER,
        synced_at INTEGER
      )
    ''');

    // Destinations table (cached from Strapi)
    await db.execute('''
      CREATE TABLE destinations (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        slug TEXT UNIQUE,
        description TEXT,
        excerpt TEXT,
        featured_image TEXT,
        gallery TEXT,
        latitude REAL,
        longitude REAL,
        country TEXT DEFAULT 'Albania',
        region TEXT,
        highlights TEXT,
        category TEXT,
        rating REAL DEFAULT 0.0,
        review_count INTEGER DEFAULT 0,
        published INTEGER DEFAULT 0,
        created_at INTEGER,
        updated_at INTEGER,
        synced_at INTEGER
      )
    ''');

    // Posts table (cached from Strapi)
    await db.execute('''
      CREATE TABLE posts (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        slug TEXT UNIQUE,
        excerpt TEXT,
        content TEXT,
        featured_image TEXT,
        gallery TEXT,
        category TEXT,
        tags TEXT,
        author TEXT,
        author_image TEXT,
        published INTEGER DEFAULT 0,
        published_at INTEGER,
        created_at INTEGER,
        updated_at INTEGER,
        synced_at INTEGER
      )
    ''');

    // Itineraries table (cached from Strapi)
    await db.execute('''
      CREATE TABLE itineraries (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        slug TEXT UNIQUE,
        description TEXT,
        duration INTEGER,
        price REAL,
        currency TEXT DEFAULT 'EUR',
        difficulty TEXT,
        highlights TEXT,
        includes TEXT,
        excludes TEXT,
        destinations TEXT,
        featured_image TEXT,
        gallery TEXT,
        published INTEGER DEFAULT 0,
        created_at INTEGER,
        updated_at INTEGER,
        synced_at INTEGER
      )
    ''');

    // Bookings table (local + Firebase sync)
    await db.execute('''
      CREATE TABLE bookings (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        itinerary_id TEXT NOT NULL,
        itinerary_title TEXT,
        status TEXT DEFAULT 'pending',
        start_date INTEGER,
        end_date INTEGER,
        total_price REAL,
        travelers INTEGER DEFAULT 1,
        traveler_details TEXT,
        created_at INTEGER,
        updated_at INTEGER,
        synced_at INTEGER,
        needs_sync INTEGER DEFAULT 1
      )
    ''');

    // Favorites table (local + Firebase sync)
    await db.execute('''
      CREATE TABLE favorites (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        item_id TEXT NOT NULL,
        item_type TEXT NOT NULL,
        item_title TEXT,
        item_image TEXT,
        created_at INTEGER,
        synced_at INTEGER,
        needs_sync INTEGER DEFAULT 1,
        UNIQUE(user_id, item_id, item_type)
      )
    ''');

    // Reviews table (local + Firebase sync)
    await db.execute('''
      CREATE TABLE reviews (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        user_name TEXT,
        user_image TEXT,
        item_id TEXT NOT NULL,
        item_type TEXT NOT NULL,
        rating REAL NOT NULL,
        comment TEXT,
        images TEXT,
        created_at INTEGER,
        synced_at INTEGER,
        needs_sync INTEGER DEFAULT 1
      )
    ''');

    // Travel history table (local + Firebase sync)
    await db.execute('''
      CREATE TABLE travel_history (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        destination_id TEXT,
        destination_name TEXT,
        visit_date INTEGER,
        duration INTEGER,
        rating REAL,
        notes TEXT,
        photos TEXT,
        created_at INTEGER,
        synced_at INTEGER,
        needs_sync INTEGER DEFAULT 1
      )
    ''');

    // Offline content queue
    await db.execute('''
      CREATE TABLE sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        action TEXT NOT NULL,
        data TEXT,
        created_at INTEGER,
        retry_count INTEGER DEFAULT 0
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_destinations_region ON destinations(region)');
    await db.execute('CREATE INDEX idx_destinations_category ON destinations(category)');
    await db.execute('CREATE INDEX idx_posts_category ON posts(category)');
    await db.execute('CREATE INDEX idx_posts_published ON posts(published)');
    await db.execute('CREATE INDEX idx_bookings_user ON bookings(user_id)');
    await db.execute('CREATE INDEX idx_favorites_user ON favorites(user_id)');
    await db.execute('CREATE INDEX idx_reviews_item ON reviews(item_id, item_type)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  // User operations
  Future<void> cacheUser(UserModel user) async {
    if (kIsWeb) {
      // For web, store in memory or localStorage
      print('📝 Caching user in memory: ${user.name}');
      return;
    }

    final db = await database;
    if (db == null) return;

    await db.insert(
      'users',
      {
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'profile_image': user.profileImage,
        'preferences': jsonEncode(user.preferences.toMap()),
        'travel_stats': jsonEncode(user.travelStats.toMap()),
        'created_at': user.createdAt.millisecondsSinceEpoch,
        'updated_at': user.updatedAt?.millisecondsSinceEpoch,
        'synced_at': DateTime.now().millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<UserModel?> getCachedUser(String userId) async {
    if (kIsWeb) {
      // For web, return null (always fetch from Firebase)
      return null;
    }

    final db = await database;
    if (db == null) return null;

    final maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [userId],
    );

    if (maps.isNotEmpty) {
      final map = maps.first;
      return UserModel(
        id: map['id'] as String,
        name: map['name'] as String,
        email: map['email'] as String,
        profileImage: map['profile_image'] as String? ?? '',
        preferences: UserPreferences.fromMap(
          jsonDecode(map['preferences'] as String? ?? '{}')
        ),
        travelStats: TravelStats.fromMap(
          jsonDecode(map['travel_stats'] as String? ?? '{}')
        ),
        createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
        updatedAt: map['updated_at'] != null 
            ? DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int)
            : null,
      );
    }
    return null;
  }

  // Destinations operations
  Future<void> cacheDestinations(List<DestinationPost> destinations) async {
    if (kIsWeb) return; // Skip on web

    final db = await database;
    if (db == null) return;

    final batch = db.batch();

    for (final dest in destinations) {
      batch.insert(
        'destinations',
        {
          'id': dest.id,
          'title': dest.title,
          'slug': dest.slug,
          'description': dest.content,
          'excerpt': dest.excerpt,
          'featured_image': dest.featuredImage,
          'gallery': jsonEncode(dest.gallery),
          'latitude': dest.latitude,
          'longitude': dest.longitude,
          'country': dest.country,
          'region': dest.region,
          'highlights': jsonEncode(dest.highlights),
          'category': dest.category,
          'published': dest.published ? 1 : 0,
          'created_at': dest.createdAt.millisecondsSinceEpoch,
          'updated_at': dest.updatedAt.millisecondsSinceEpoch,
          'synced_at': DateTime.now().millisecondsSinceEpoch,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  Future<List<Map<String, dynamic>>> getCachedDestinations({
    String? region,
    String? category,
    int limit = 20,
  }) async {
    if (_shouldSkipDatabase) return [];

    final db = await database;
    if (db == null) return [];

    String whereClause = 'published = 1';
    List<dynamic> whereArgs = [];

    if (region != null) {
      whereClause += ' AND region = ?';
      whereArgs.add(region);
    }

    if (category != null) {
      whereClause += ' AND category = ?';
      whereArgs.add(category);
    }

    return await db.query(
      'destinations',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
      limit: limit,
    );
  }

  // Posts operations
  Future<void> cachePosts(List<CMSPost> posts) async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    final batch = db.batch();

    for (final post in posts) {
      batch.insert(
        'posts',
        {
          'id': post.id,
          'title': post.title,
          'slug': post.slug,
          'excerpt': post.excerpt,
          'content': post.content,
          'featured_image': post.featuredImage,
          'gallery': jsonEncode(post.gallery),
          'category': post.category,
          'tags': jsonEncode(post.tags),
          'author': post.author,
          'author_image': post.authorImage,
          'published': post.published ? 1 : 0,
          'published_at': post.publishedAt.millisecondsSinceEpoch,
          'created_at': post.createdAt.millisecondsSinceEpoch,
          'updated_at': post.updatedAt.millisecondsSinceEpoch,
          'synced_at': DateTime.now().millisecondsSinceEpoch,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  Future<List<Map<String, dynamic>>> getCachedPosts({
    String? category,
    int limit = 10,
  }) async {
    if (_shouldSkipDatabase) return [];

    final db = await database;
    if (db == null) return [];

    String whereClause = 'published = 1';
    List<dynamic> whereArgs = [];

    if (category != null) {
      whereClause += ' AND category = ?';
      whereArgs.add(category);
    }

    return await db.query(
      'posts',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'published_at DESC',
      limit: limit,
    );
  }

  // Bookings operations
  Future<String> createBooking(Map<String, dynamic> bookingData) async {
    if (_shouldSkipDatabase) return DateTime.now().millisecondsSinceEpoch.toString();

    final db = await database;
    if (db == null) return DateTime.now().millisecondsSinceEpoch.toString();

    final id = DateTime.now().millisecondsSinceEpoch.toString();

    await db.insert('bookings', {
      'id': id,
      ...bookingData,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
      'needs_sync': 1,
    });

    // Add to sync queue
    await _addToSyncQueue('bookings', id, 'create', bookingData);

    return id;
  }

  Future<List<Map<String, dynamic>>> getUserBookings(String userId) async {
    if (_shouldSkipDatabase) return [];

    final db = await database;
    if (db == null) return [];

    return await db.query(
      'bookings',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );
  }

  // Favorites operations
  Future<void> addToFavorites(String userId, String itemId, String itemType, {
    String? itemTitle,
    String? itemImage,
  }) async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    final id = '${userId}_${itemId}_$itemType';

    await db.insert(
      'favorites',
      {
        'id': id,
        'user_id': userId,
        'item_id': itemId,
        'item_type': itemType,
        'item_title': itemTitle,
        'item_image': itemImage,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'needs_sync': 1,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    await _addToSyncQueue('favorites', id, 'create', {
      'user_id': userId,
      'item_id': itemId,
      'item_type': itemType,
    });
  }

  Future<void> removeFromFavorites(String userId, String itemId, String itemType) async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    final id = '${userId}_${itemId}_$itemType';

    await db.delete(
      'favorites',
      where: 'user_id = ? AND item_id = ? AND item_type = ?',
      whereArgs: [userId, itemId, itemType],
    );

    await _addToSyncQueue('favorites', id, 'delete', {});
  }

  Future<List<Map<String, dynamic>>> getUserFavorites(String userId, String itemType) async {
    if (_shouldSkipDatabase) return [];

    final db = await database;
    if (db == null) return [];

    return await db.query(
      'favorites',
      where: 'user_id = ? AND item_type = ?',
      whereArgs: [userId, itemType],
      orderBy: 'created_at DESC',
    );
  }

  // Sync queue operations
  Future<void> _addToSyncQueue(String tableName, String recordId, String action, Map<String, dynamic> data) async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    await db.insert('sync_queue', {
      'table_name': tableName,
      'record_id': recordId,
      'action': action,
      'data': jsonEncode(data),
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'retry_count': 0,
    });
  }

  Future<List<Map<String, dynamic>>> getPendingSyncItems() async {
    if (_shouldSkipDatabase) return [];

    final db = await database;
    if (db == null) return [];

    return await db.query(
      'sync_queue',
      where: 'retry_count < 3',
      orderBy: 'created_at ASC',
      limit: 50,
    );
  }

  Future<void> markSyncComplete(int syncId) async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    await db.delete('sync_queue', where: 'id = ?', whereArgs: [syncId]);
  }

  Future<void> incrementSyncRetry(int syncId) async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    await db.update(
      'sync_queue',
      {'retry_count': 'retry_count + 1'},
      where: 'id = ?',
      whereArgs: [syncId],
    );
  }

  // Search operations
  Future<List<Map<String, dynamic>>> searchContent(String query) async {
    if (_shouldSkipDatabase) return [];

    final db = await database;
    if (db == null) return [];

    final searchTerm = '%$query%';

    // Search destinations
    final destinations = await db.query(
      'destinations',
      where: 'published = 1 AND (title LIKE ? OR description LIKE ?)',
      whereArgs: [searchTerm, searchTerm],
      limit: 10,
    );

    // Search posts
    final posts = await db.query(
      'posts',
      where: 'published = 1 AND (title LIKE ? OR content LIKE ?)',
      whereArgs: [searchTerm, searchTerm],
      limit: 10,
    );

    return [
      ...destinations.map((d) => {...d, 'type': 'destination'}),
      ...posts.map((p) => {...p, 'type': 'post'}),
    ];
  }

  // Clear cache
  Future<void> clearCache() async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    await db.delete('destinations');
    await db.delete('posts');
    await db.delete('itineraries');
  }

  // Close database
  Future<void> close() async {
    if (_shouldSkipDatabase) return;

    final db = await database;
    if (db == null) return;

    await db.close();
  }
}
