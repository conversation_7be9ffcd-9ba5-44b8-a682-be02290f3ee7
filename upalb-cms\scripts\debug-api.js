const axios = require('axios');

const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';
const STRAPI_URL = 'http://localhost:1337';

async function debugAPI() {
  try {
    console.log('🔍 Debugging Strapi API...');
    
    // Test 1: Check if server is running
    console.log('\n1. Testing server connection...');
    try {
      const healthCheck = await axios.get(`${STRAPI_URL}/_health`);
      console.log('✅ Server is running');
    } catch (error) {
      console.log('❌ Server connection failed:', error.message);
      return;
    }
    
    // Test 2: Check available routes
    console.log('\n2. Testing available API routes...');
    const routes = [
      '/api/destinations',
      '/api/hotels', 
      '/api/restaurants',
      '/api/activities',
      '/api/travel-tips'
    ];
    
    for (const route of routes) {
      try {
        const response = await axios.get(`${STRAPI_URL}${route}`, {
          headers: {
            'Authorization': `Bearer ${API_TOKEN}`
          }
        });
        console.log(`✅ GET ${route} - Status: ${response.status}`);
      } catch (error) {
        console.log(`❌ GET ${route} - Status: ${error.response?.status} - ${error.response?.statusText}`);
      }
    }
    
    // Test 3: Check POST permissions
    console.log('\n3. Testing POST permissions...');
    const testData = {
      data: {
        name: "Test Item",
        description: "Test description"
      }
    };
    
    for (const route of routes) {
      try {
        const response = await axios.post(`${STRAPI_URL}${route}`, testData, {
          headers: {
            'Authorization': `Bearer ${API_TOKEN}`,
            'Content-Type': 'application/json'
          }
        });
        console.log(`✅ POST ${route} - Status: ${response.status}`);
        
        // Clean up - delete the test item
        if (response.data?.data?.id) {
          await axios.delete(`${STRAPI_URL}${route}/${response.data.data.id}`, {
            headers: {
              'Authorization': `Bearer ${API_TOKEN}`
            }
          });
        }
      } catch (error) {
        console.log(`❌ POST ${route} - Status: ${error.response?.status} - ${error.response?.statusText}`);
        if (error.response?.data) {
          console.log(`   Error details:`, JSON.stringify(error.response.data, null, 2));
        }
      }
    }
    
    // Test 4: Check token validity
    console.log('\n4. Testing token validity...');
    try {
      const response = await axios.get(`${STRAPI_URL}/api/users/me`, {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`
        }
      });
      console.log('✅ Token is valid for user endpoint');
    } catch (error) {
      console.log('❌ Token validation failed:', error.response?.status, error.response?.statusText);
    }
    
  } catch (error) {
    console.error('💥 Debug failed:', error.message);
  }
}

debugAPI();
