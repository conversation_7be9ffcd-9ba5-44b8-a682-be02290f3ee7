{"collectionName": "components_travel_transportation", "info": {"displayName": "Transportation", "description": "Transportation options and information"}, "options": {}, "attributes": {"options": {"type": "json"}, "nearestAirport": {"type": "string"}, "airportDistance": {"type": "string"}, "publicTransport": {"type": "text"}, "carRental": {"type": "boolean", "default": false}, "parkingAvailable": {"type": "boolean", "default": false}, "walkingDistance": {"type": "text"}, "accessibility": {"type": "text"}}}