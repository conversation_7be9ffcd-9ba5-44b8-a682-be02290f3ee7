import 'package:cloud_firestore/cloud_firestore.dart';

class CMSPost {
  final String id;
  final String title;
  final String slug;
  final String excerpt;
  final String content;
  final String featuredImage;
  final List<String> gallery;
  final String category;
  final List<String> tags;
  final String author;
  final String authorImage;
  final bool published;
  final DateTime publishedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> seo;
  final Map<String, dynamic> metadata;

  CMSPost({
    required this.id,
    required this.title,
    required this.slug,
    required this.excerpt,
    required this.content,
    required this.featuredImage,
    required this.gallery,
    required this.category,
    required this.tags,
    required this.author,
    required this.authorImage,
    required this.published,
    required this.publishedAt,
    required this.createdAt,
    required this.updatedAt,
    required this.seo,
    required this.metadata,
  });

  factory CMSPost.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CMSPost(
      id: doc.id,
      title: data['title'] ?? '',
      slug: data['slug'] ?? '',
      excerpt: data['excerpt'] ?? '',
      content: data['content'] ?? '',
      featuredImage: data['featuredImage'] ?? '',
      gallery: List<String>.from(data['gallery'] ?? []),
      category: data['category'] ?? '',
      tags: List<String>.from(data['tags'] ?? []),
      author: data['author'] ?? '',
      authorImage: data['authorImage'] ?? '',
      published: data['published'] ?? false,
      publishedAt: data['publishedAt'] != null 
          ? (data['publishedAt'] as Timestamp).toDate()
          : DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      seo: Map<String, dynamic>.from(data['seo'] ?? {}),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  factory CMSPost.fromStrapi(Map<String, dynamic> data) {
    final attributes = data['attributes'] ?? {};
    return CMSPost(
      id: data['id'].toString(),
      title: attributes['title'] ?? '',
      slug: attributes['slug'] ?? '',
      excerpt: attributes['excerpt'] ?? '',
      content: attributes['content'] ?? '',
      featuredImage: _extractImageUrl(attributes['featuredImage']),
      gallery: _extractGalleryUrls(attributes['gallery']),
      category: _extractCategoryName(attributes['category']),
      tags: _extractTagNames(attributes['tags']),
      author: _extractAuthorName(attributes['author']),
      authorImage: _extractAuthorImage(attributes['author']),
      published: attributes['published'] ?? false,
      publishedAt: DateTime.parse(attributes['publishedAt'] ?? DateTime.now().toIso8601String()),
      createdAt: DateTime.parse(attributes['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(attributes['updatedAt'] ?? DateTime.now().toIso8601String()),
      seo: Map<String, dynamic>.from(attributes['seo'] ?? {}),
      metadata: Map<String, dynamic>.from(attributes['metadata'] ?? {}),
    );
  }

  static String _extractImageUrl(dynamic imageData) {
    if (imageData == null) return '';
    if (imageData['data'] == null) return '';
    return imageData['data']['attributes']['url'] ?? '';
  }

  static List<String> _extractGalleryUrls(dynamic galleryData) {
    if (galleryData == null || galleryData['data'] == null) return [];
    return (galleryData['data'] as List)
        .map((item) => item['attributes']['url'] as String)
        .toList();
  }

  static String _extractCategoryName(dynamic categoryData) {
    if (categoryData == null || categoryData['data'] == null) return '';
    return categoryData['data']['attributes']['name'] ?? '';
  }

  static List<String> _extractTagNames(dynamic tagsData) {
    if (tagsData == null || tagsData['data'] == null) return [];
    return (tagsData['data'] as List)
        .map((item) => item['attributes']['name'] as String)
        .toList();
  }

  static String _extractAuthorName(dynamic authorData) {
    if (authorData == null || authorData['data'] == null) return '';
    return authorData['data']['attributes']['name'] ?? '';
  }

  static String _extractAuthorImage(dynamic authorData) {
    if (authorData == null || authorData['data'] == null) return '';
    final avatar = authorData['data']['attributes']['avatar'];
    if (avatar == null || avatar['data'] == null) return '';
    return avatar['data']['attributes']['url'] ?? '';
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'slug': slug,
      'excerpt': excerpt,
      'content': content,
      'featuredImage': featuredImage,
      'gallery': gallery,
      'category': category,
      'tags': tags,
      'author': author,
      'authorImage': authorImage,
      'published': published,
      'publishedAt': Timestamp.fromDate(publishedAt),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'seo': seo,
      'metadata': metadata,
      'syncedAt': FieldValue.serverTimestamp(),
    };
  }
}

class CMSCategory {
  final String id;
  final String name;
  final String slug;
  final String description;
  final String image;
  final int postCount;

  CMSCategory({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.image,
    required this.postCount,
  });

  factory CMSCategory.fromStrapi(Map<String, dynamic> data) {
    final attributes = data['attributes'] ?? {};
    return CMSCategory(
      id: data['id'].toString(),
      name: attributes['name'] ?? '',
      slug: attributes['slug'] ?? '',
      description: attributes['description'] ?? '',
      image: CMSPost._extractImageUrl(attributes['image']),
      postCount: attributes['postCount'] ?? 0,
    );
  }
}

class CMSService {
  static const String strapiBaseUrl = 'https://your-strapi-instance.com/api';
  
  // Fetch posts from Strapi CMS
  static Future<List<CMSPost>> fetchPosts({
    String? category,
    List<String>? tags,
    int page = 1,
    int pageSize = 10,
  }) async {
    // Implementation for fetching from Strapi
    // This would use http package to make API calls
    return [];
  }

  // Fetch categories from Strapi CMS
  static Future<List<CMSCategory>> fetchCategories() async {
    // Implementation for fetching categories
    return [];
  }

  // Sync CMS content to Firebase
  static Future<void> syncToFirebase() async {
    final posts = await fetchPosts();
    // TODO: Implement Firebase sync
    // final firebaseService = FirebaseService();
    // final postsData = posts.map((post) => post.toFirestore()).toList();
    // await firebaseService.syncCMSContent(postsData);
  }
}

// Post types for different content
enum PostType {
  destination('destination'),
  guide('guide'),
  news('news'),
  review('review'),
  itinerary('itinerary'),
  culture('culture'),
  food('food'),
  accommodation('accommodation');

  const PostType(this.value);
  final String value;
}

class DestinationPost extends CMSPost {
  final double latitude;
  final double longitude;
  final String country;
  final String region;
  final List<String> highlights;
  final Map<String, dynamic> weather;
  final Map<String, dynamic> transportation;

  DestinationPost({
    required super.id,
    required super.title,
    required super.slug,
    required super.excerpt,
    required super.content,
    required super.featuredImage,
    required super.gallery,
    required super.category,
    required super.tags,
    required super.author,
    required super.authorImage,
    required super.published,
    required super.publishedAt,
    required super.createdAt,
    required super.updatedAt,
    required super.seo,
    required super.metadata,
    required this.latitude,
    required this.longitude,
    required this.country,
    required this.region,
    required this.highlights,
    required this.weather,
    required this.transportation,
  });

  factory DestinationPost.fromCMSPost(CMSPost post) {
    final metadata = post.metadata;
    return DestinationPost(
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      featuredImage: post.featuredImage,
      gallery: post.gallery,
      category: post.category,
      tags: post.tags,
      author: post.author,
      authorImage: post.authorImage,
      published: post.published,
      publishedAt: post.publishedAt,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
      seo: post.seo,
      metadata: post.metadata,
      latitude: (metadata['latitude'] ?? 0.0).toDouble(),
      longitude: (metadata['longitude'] ?? 0.0).toDouble(),
      country: metadata['country'] ?? '',
      region: metadata['region'] ?? '',
      highlights: List<String>.from(metadata['highlights'] ?? []),
      weather: Map<String, dynamic>.from(metadata['weather'] ?? {}),
      transportation: Map<String, dynamic>.from(metadata['transportation'] ?? {}),
    );
  }
}
