import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:io';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  // Initialize Google Sign-In lazily to avoid web configuration errors
  GoogleSignIn? _googleSignIn;

  // Current user
  User? get currentUser => _auth.currentUser;
  String? get currentUserId => _auth.currentUser?.uid;

  // Authentication
  Future<UserCredential?> signInWithEmail(String email, String password) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      print('Sign in error: $e');
      rethrow; // Re-throw the error so AppService can handle it properly
    }
  }

  Future<UserCredential?> signUpWithEmail(String email, String password, String name) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Create user profile
      if (credential.user != null) {
        await createUserProfile(credential.user!.uid, {
          'name': name,
          'email': email,
          'createdAt': FieldValue.serverTimestamp(),
          'profileImage': '',
          'preferences': {
            'language': 'en',
            'currency': 'EUR',
            'notifications': true,
          },
          'travelStats': {
            'tripsCompleted': 0,
            'placesVisited': 0,
            'totalSpent': 0.0,
          }
        });
      }
      
      return credential;
    } catch (e) {
      print('Sign up error: $e');
      return null;
    }
  }

  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Initialize Google Sign-In if not already done
      _googleSignIn ??= GoogleSignIn();

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final userCredential = await _auth.signInWithCredential(credential);

      // Create or update user profile
      if (userCredential.user != null) {
        final user = userCredential.user!;

        if (userCredential.additionalUserInfo?.isNewUser == true) {
          // Create new user profile
          await createUserProfile(user.uid, {
            'name': user.displayName ?? 'Google User',
            'email': user.email ?? '',
            'profileImage': user.photoURL ?? '',
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
            'preferences': {
              'language': 'en',
              'currency': 'EUR',
              'notifications': true,
            },
            'travelStats': {
              'tripsCompleted': 0,
              'visitedCountries': [],
              'reviewsWritten': 0,
              'totalDistance': 0,
            }
          });
        } else {
          // Update existing user profile with latest Google info
          await updateUserProfile(user.uid, {
            'name': user.displayName ?? 'Google User',
            'email': user.email ?? '',
            'profileImage': user.photoURL ?? '',
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      }

      return userCredential;
    } catch (e) {
      print('Google sign in error: $e');
      return null;
    }
  }

  Future<void> signOut() async {
    await _googleSignIn?.signOut();
    await _auth.signOut();
  }

  // User Profile Management
  Future<void> createUserProfile(String userId, Map<String, dynamic> userData) async {
    await _firestore.collection('users').doc(userId).set(userData);
  }

  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      return doc.exists ? doc.data() : null;
    } catch (e) {
      print('Get user profile error: $e');
      return null;
    }
  }

  Future<void> updateUserProfile(String userId, Map<String, dynamic> updates) async {
    await _firestore.collection('users').doc(userId).update(updates);
  }

  Stream<DocumentSnapshot> getUserProfileStream(String userId) {
    return _firestore.collection('users').doc(userId).snapshots();
  }

  // Bookings Management
  Future<String?> createBooking(Map<String, dynamic> bookingData) async {
    try {
      final docRef = await _firestore.collection('bookings').add({
        ...bookingData,
        'userId': currentUserId,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return docRef.id;
    } catch (e) {
      print('Create booking error: $e');
      return null;
    }
  }

  Stream<QuerySnapshot> getUserBookings(String userId) {
    return _firestore
        .collection('bookings')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  Future<void> updateBookingStatus(String bookingId, String status) async {
    await _firestore.collection('bookings').doc(bookingId).update({
      'status': status,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  // Favorites Management
  Future<void> addToFavorites(String userId, String itemId, String itemType) async {
    await _firestore.collection('favorites').add({
      'userId': userId,
      'itemId': itemId,
      'itemType': itemType, // 'destination', 'itinerary', 'post'
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  Future<void> removeFromFavorites(String userId, String itemId) async {
    final query = await _firestore
        .collection('favorites')
        .where('userId', isEqualTo: userId)
        .where('itemId', isEqualTo: itemId)
        .get();
    
    for (var doc in query.docs) {
      await doc.reference.delete();
    }
  }

  Stream<QuerySnapshot> getUserFavorites(String userId, String itemType) {
    return _firestore
        .collection('favorites')
        .where('userId', isEqualTo: userId)
        .where('itemType', isEqualTo: itemType)
        .snapshots();
  }

  // Travel History
  Future<void> addTravelHistory(String userId, Map<String, dynamic> travelData) async {
    await _firestore.collection('travelHistory').add({
      ...travelData,
      'userId': userId,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  Stream<QuerySnapshot> getUserTravelHistory(String userId) {
    return _firestore
        .collection('travelHistory')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  // Reviews Management
  Future<void> addReview(Map<String, dynamic> reviewData) async {
    await _firestore.collection('reviews').add({
      ...reviewData,
      'userId': currentUserId,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  Stream<QuerySnapshot> getItemReviews(String itemId, String itemType) {
    return _firestore
        .collection('reviews')
        .where('itemId', isEqualTo: itemId)
        .where('itemType', isEqualTo: itemType)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  // File Upload
  Future<String?> uploadImage(File file, String path) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = await ref.putFile(file);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      print('Upload error: $e');
      return null;
    }
  }

  // CMS Content (Posts from headless CMS)
  Future<void> syncCMSContent(List<Map<String, dynamic>> posts) async {
    final batch = _firestore.batch();
    
    for (var post in posts) {
      final docRef = _firestore.collection('posts').doc(post['id']);
      batch.set(docRef, {
        ...post,
        'syncedAt': FieldValue.serverTimestamp(),
      });
    }
    
    await batch.commit();
  }

  Stream<QuerySnapshot> getPosts({String? category, int limit = 10}) {
    Query query = _firestore
        .collection('posts')
        .where('published', isEqualTo: true)
        .orderBy('publishedAt', descending: true)
        .limit(limit);
    
    if (category != null) {
      query = query.where('category', isEqualTo: category);
    }
    
    return query.snapshots();
  }

  // Analytics & User Behavior
  Future<void> trackUserAction(String action, Map<String, dynamic> data) async {
    if (currentUserId != null) {
      await _firestore.collection('userAnalytics').add({
        'userId': currentUserId,
        'action': action,
        'data': data,
        'timestamp': FieldValue.serverTimestamp(),
      });
    }
  }
}
