class DestinationModel {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String? shortDescription;
  final String? featuredImage;
  final List<String>? gallery;
  final double? latitude;
  final double? longitude;
  final String? address;
  final String region;
  final String category;
  final String difficulty;
  final List<String>? bestTimeToVisit;
  final String estimatedDuration;
  final double entryFee;
  final String currency;
  final bool isPopular;
  final bool isFeatured;
  final double rating;
  final int reviewCount;
  final List<String>? tags;
  final String? weatherInfo;
  final String? transportationInfo;
  final String localLanguage;
  final String? emergencyContacts;
  final DateTime createdAt;
  final DateTime updatedAt;

  DestinationModel({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    this.shortDescription,
    this.featuredImage,
    this.gallery,
    this.latitude,
    this.longitude,
    this.address,
    required this.region,
    required this.category,
    required this.difficulty,
    this.bestTimeToVisit,
    required this.estimatedDuration,
    required this.entryFee,
    required this.currency,
    required this.isPopular,
    required this.isFeatured,
    required this.rating,
    required this.reviewCount,
    this.tags,
    this.weatherInfo,
    this.transportationInfo,
    required this.localLanguage,
    this.emergencyContacts,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DestinationModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? json;
    
    return DestinationModel(
      id: json['id'] ?? 0,
      name: attributes['name'] ?? '',
      slug: attributes['slug'] ?? '',
      description: attributes['description'] ?? '',
      shortDescription: attributes['shortDescription'],
      featuredImage: attributes['featuredImage']?['data']?['attributes']?['url'],
      gallery: attributes['gallery']?['data']?.map<String>((item) => 
        item['attributes']['url'] as String).toList(),
      latitude: attributes['latitude']?.toDouble(),
      longitude: attributes['longitude']?.toDouble(),
      address: attributes['address'],
      region: attributes['region'] ?? '',
      category: attributes['category'] ?? '',
      difficulty: attributes['difficulty'] ?? 'Easy',
      bestTimeToVisit: attributes['bestTimeToVisit']?.cast<String>(),
      estimatedDuration: attributes['estimatedDuration'] ?? '1 day',
      entryFee: (attributes['entryFee'] ?? 0).toDouble(),
      currency: attributes['currency'] ?? 'ALL',
      isPopular: attributes['isPopular'] ?? false,
      isFeatured: attributes['isFeatured'] ?? false,
      rating: (attributes['rating'] ?? 0).toDouble(),
      reviewCount: attributes['reviewCount'] ?? 0,
      tags: attributes['tags']?.cast<String>(),
      weatherInfo: attributes['weatherInfo'],
      transportationInfo: attributes['transportationInfo'],
      localLanguage: attributes['localLanguage'] ?? 'Albanian',
      emergencyContacts: attributes['emergencyContacts'],
      createdAt: DateTime.parse(attributes['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(attributes['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'attributes': {
        'name': name,
        'slug': slug,
        'description': description,
        'shortDescription': shortDescription,
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
        'region': region,
        'category': category,
        'difficulty': difficulty,
        'bestTimeToVisit': bestTimeToVisit,
        'estimatedDuration': estimatedDuration,
        'entryFee': entryFee,
        'currency': currency,
        'isPopular': isPopular,
        'isFeatured': isFeatured,
        'rating': rating,
        'reviewCount': reviewCount,
        'tags': tags,
        'weatherInfo': weatherInfo,
        'transportationInfo': transportationInfo,
        'localLanguage': localLanguage,
        'emergencyContacts': emergencyContacts,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      }
    };
  }
}
