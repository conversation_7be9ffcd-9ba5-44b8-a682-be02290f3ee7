class Destination {
  final String id;
  final String name;
  final String country;
  final String imageUrl;
  final double rating;
  final int price;
  final String description;
  final List<String> tags;
  final List<String> images;

  Destination({
    required this.id,
    required this.name,
    required this.country,
    required this.imageUrl,
    required this.rating,
    required this.price,
    required this.description,
    this.tags = const [],
    this.images = const [],
  });

  factory Destination.fromJson(Map<String, dynamic> json) {
    return Destination(
      id: json['id'],
      name: json['name'],
      country: json['country'],
      imageUrl: json['imageUrl'],
      rating: json['rating'].toDouble(),
      price: json['price'],
      description: json['description'],
      tags: List<String>.from(json['tags'] ?? []),
      images: List<String>.from(json['images'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'country': country,
      'imageUrl': imageUrl,
      'rating': rating,
      'price': price,
      'description': description,
      'tags': tags,
      'images': images,
    };
  }
}
