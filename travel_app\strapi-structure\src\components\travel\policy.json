{"collectionName": "components_travel_policies", "info": {"displayName": "Policy", "description": "Hotel or service policy"}, "options": {}, "attributes": {"title": {"type": "string", "required": true}, "description": {"type": "richtext", "required": true}, "type": {"type": "enumeration", "enum": ["Cancellation", "Check-in/Check-out", "Pet Policy", "Smoking Policy", "Age Restrictions", "Payment", "Damage Policy", "Noise Policy", "General"], "default": "General"}, "important": {"type": "boolean", "default": false}}}