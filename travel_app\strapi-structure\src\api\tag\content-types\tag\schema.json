{"kind": "collectionType", "collectionName": "tags", "info": {"singularName": "tag", "pluralName": "tags", "displayName": "Tag", "description": "Tags for content organization and filtering"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "color": {"type": "string", "default": "#00CFC8"}, "destinations": {"type": "relation", "relation": "manyToMany", "target": "api::destination.destination", "inversedBy": "tags"}, "blogPosts": {"type": "relation", "relation": "manyToMany", "target": "api::blog-post.blog-post", "inversedBy": "tags"}, "hotels": {"type": "relation", "relation": "manyToMany", "target": "api::hotel.hotel", "inversedBy": "tags"}, "restaurants": {"type": "relation", "relation": "manyToMany", "target": "api::restaurant.restaurant", "inversedBy": "tags"}, "itineraries": {"type": "relation", "relation": "manyToMany", "target": "api::itinerary.itinerary", "inversedBy": "tags"}, "featured": {"type": "boolean", "default": false}}}