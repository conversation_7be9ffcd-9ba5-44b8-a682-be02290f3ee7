{"collectionName": "components_travel_activities", "info": {"displayName": "Activity", "description": "Activities and things to do"}, "options": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "type": {"type": "enumeration", "enum": ["Sightseeing", "Adventure", "Cultural", "Nature", "Food & Drink", "Shopping", "Entertainment", "Sports", "Relaxation"], "required": true}, "duration": {"type": "string"}, "difficulty": {"type": "enumeration", "enum": ["Easy", "Moderate", "Challenging", "Expert"], "default": "Easy"}, "price": {"type": "decimal", "min": 0}, "currency": {"type": "string", "default": "EUR"}, "seasonality": {"type": "json"}, "bookingRequired": {"type": "boolean", "default": false}, "bookingUrl": {"type": "string"}, "recommended": {"type": "boolean", "default": false}}}