const axios = require('axios');

const API_TOKEN = 'ac97e6985373ba9592f7f1be36ace328bc4cf2bea9e01db04fcb8ffa3951de72b871af2ea8d3831d53f7a2564892e8bc8ebf542ab3ac5a11694c1f484903218ac965c79b90a6ab897f737d6579280cb9e1bef0f579f8d30e674e0b8b368253b92d31f23ac4111b18873c1a0be87ef4f3e2430245d8b22cd54c68aba509d9372e';

async function testAPI() {
  try {
    console.log('🔍 Testing Strapi API...');

    // Test GET request without auth
    try {
      const response = await axios.get('http://localhost:1337/api/destinations');
      console.log('✅ Public API is accessible');
      console.log('Response:', response.data);
    } catch (error) {
      console.log('❌ Public API not accessible:', error.response?.status);
    }

    // Test GET request with auth
    try {
      const response = await axios.get('http://localhost:1337/api/destinations', {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`
        }
      });
      console.log('✅ Authenticated API is accessible');
      console.log('Response:', response.data);
    } catch (error) {
      console.log('❌ Authenticated API error:', error.response?.status, error.response?.statusText);
      console.log('Error details:', error.response?.data);
    }

    // Test POST request
    try {
      const testDestination = {
        name: "Test Destination",
        slug: "test-destination",
        description: "A test destination",
        shortDescription: "Test",
        region: "Tirana Region",
        category: "City",
        difficulty: "Easy",
        bestTimeToVisit: ["Summer"],
        estimatedDuration: "1 day",
        entryFee: 0,
        currency: "ALL",
        isPopular: false,
        isFeatured: false,
        latitude: 41.0,
        longitude: 19.0,
        address: "Test Address",
        rating: 4.0,
        reviewCount: 0
      };

      const response = await axios.post('http://localhost:1337/api/destinations', {
        data: testDestination
      }, {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ POST request successful');
      console.log('Created:', response.data);
    } catch (error) {
      console.log('❌ POST request failed:', error.response?.status, error.response?.statusText);
      console.log('Error details:', error.response?.data);
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testAPI();
