class HotelModel {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String excerpt;
  final String? featuredImage;
  final List<String> gallery;
  final LocationData? location;
  final int starRating;
  final String priceRange;
  final PricingData? pricePerNight;
  final List<AmenityData> amenities;
  final List<RoomTypeData> roomTypes;
  final String checkInTime;
  final String checkOutTime;
  final ContactData? contactInfo;
  final List<PolicyData> policies;
  final String region;
  final String city;
  final String country;
  final double rating;
  final int reviewCount;
  final bool featured;
  final String? bookingUrl;
  final String? website;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;

  HotelModel({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.excerpt,
    this.featuredImage,
    this.gallery = const [],
    this.location,
    required this.starRating,
    required this.priceRange,
    this.pricePerNight,
    this.amenities = const [],
    this.roomTypes = const [],
    required this.checkInTime,
    required this.checkOutTime,
    this.contactInfo,
    this.policies = const [],
    required this.region,
    required this.city,
    required this.country,
    required this.rating,
    required this.reviewCount,
    required this.featured,
    this.bookingUrl,
    this.website,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory HotelModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? {};
    
    return HotelModel(
      id: json['id'] ?? 0,
      name: attributes['name'] ?? '',
      slug: attributes['slug'] ?? '',
      description: attributes['description'] ?? '',
      excerpt: attributes['excerpt'] ?? '',
      featuredImage: _extractImageUrl(attributes['featuredImage']),
      gallery: _extractGalleryUrls(attributes['gallery']),
      location: attributes['location'] != null 
          ? LocationData.fromJson(attributes['location']) 
          : null,
      starRating: attributes['starRating'] ?? 3,
      priceRange: attributes['priceRange'] ?? 'Mid-range',
      pricePerNight: attributes['pricePerNight'] != null 
          ? PricingData.fromJson(attributes['pricePerNight']) 
          : null,
      amenities: _extractAmenities(attributes['amenities']),
      roomTypes: _extractRoomTypes(attributes['roomTypes']),
      checkInTime: attributes['checkInTime'] ?? '15:00',
      checkOutTime: attributes['checkOutTime'] ?? '11:00',
      contactInfo: attributes['contactInfo'] != null 
          ? ContactData.fromJson(attributes['contactInfo']) 
          : null,
      policies: _extractPolicies(attributes['policies']),
      region: attributes['region'] ?? 'Central Albania',
      city: attributes['city'] ?? '',
      country: attributes['country'] ?? 'Albania',
      rating: (attributes['rating'] ?? 0.0).toDouble(),
      reviewCount: attributes['reviewCount'] ?? 0,
      featured: attributes['featured'] ?? false,
      bookingUrl: attributes['bookingUrl'],
      website: attributes['website'],
      tags: _extractTags(attributes['tags']),
      createdAt: DateTime.tryParse(attributes['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(attributes['updatedAt'] ?? '') ?? DateTime.now(),
    );
  }

  static String? _extractImageUrl(dynamic imageData) {
    if (imageData == null) return null;
    if (imageData['data'] == null) return null;
    return imageData['data']['attributes']?['url'];
  }

  static List<String> _extractGalleryUrls(dynamic galleryData) {
    if (galleryData == null || galleryData['data'] == null) return [];
    final List<dynamic> images = galleryData['data'] ?? [];
    return images
        .map((img) => img['attributes']?['url'] as String?)
        .where((url) => url != null)
        .cast<String>()
        .toList();
  }

  static List<AmenityData> _extractAmenities(dynamic amenitiesData) {
    if (amenitiesData == null) return [];
    final List<dynamic> amenities = amenitiesData is List ? amenitiesData : [];
    return amenities.map((amenity) => AmenityData.fromJson(amenity)).toList();
  }

  static List<RoomTypeData> _extractRoomTypes(dynamic roomTypesData) {
    if (roomTypesData == null) return [];
    final List<dynamic> roomTypes = roomTypesData is List ? roomTypesData : [];
    return roomTypes.map((room) => RoomTypeData.fromJson(room)).toList();
  }

  static List<PolicyData> _extractPolicies(dynamic policiesData) {
    if (policiesData == null) return [];
    final List<dynamic> policies = policiesData is List ? policiesData : [];
    return policies.map((policy) => PolicyData.fromJson(policy)).toList();
  }

  static List<String> _extractTags(dynamic tagsData) {
    if (tagsData == null || tagsData['data'] == null) return [];
    final List<dynamic> tags = tagsData['data'] ?? [];
    return tags
        .map((tag) => tag['attributes']?['name'] as String?)
        .where((name) => name != null)
        .cast<String>()
        .toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'excerpt': excerpt,
      'featuredImage': featuredImage,
      'gallery': gallery,
      'location': location?.toJson(),
      'starRating': starRating,
      'priceRange': priceRange,
      'pricePerNight': pricePerNight?.toJson(),
      'amenities': amenities.map((a) => a.toJson()).toList(),
      'roomTypes': roomTypes.map((r) => r.toJson()).toList(),
      'checkInTime': checkInTime,
      'checkOutTime': checkOutTime,
      'contactInfo': contactInfo?.toJson(),
      'policies': policies.map((p) => p.toJson()).toList(),
      'region': region,
      'city': city,
      'country': country,
      'rating': rating,
      'reviewCount': reviewCount,
      'featured': featured,
      'bookingUrl': bookingUrl,
      'website': website,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class LocationData {
  final double latitude;
  final double longitude;
  final String address;
  final String city;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      address: json['address'] ?? '',
      city: json['city'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
    };
  }
}

class PricingData {
  final double basePrice;
  final String currency;
  final String priceType;

  PricingData({
    required this.basePrice,
    required this.currency,
    required this.priceType,
  });

  factory PricingData.fromJson(Map<String, dynamic> json) {
    return PricingData(
      basePrice: (json['basePrice'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'EUR',
      priceType: json['priceType'] ?? 'per_night',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'basePrice': basePrice,
      'currency': currency,
      'priceType': priceType,
    };
  }
}

class AmenityData {
  final String name;
  final String? icon;
  final String? description;
  final String category;
  final bool available;
  final bool additionalCost;

  AmenityData({
    required this.name,
    this.icon,
    this.description,
    required this.category,
    required this.available,
    required this.additionalCost,
  });

  factory AmenityData.fromJson(Map<String, dynamic> json) {
    return AmenityData(
      name: json['name'] ?? '',
      icon: json['icon'],
      description: json['description'],
      category: json['category'] ?? 'General',
      available: json['available'] ?? true,
      additionalCost: json['additionalCost'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': icon,
      'description': description,
      'category': category,
      'available': available,
      'additionalCost': additionalCost,
    };
  }
}

class RoomTypeData {
  final String name;
  final String? description;
  final int maxOccupancy;
  final String? bedType;
  final int? size;
  final String sizeUnit;
  final double? pricePerNight;
  final String currency;
  final bool available;

  RoomTypeData({
    required this.name,
    this.description,
    required this.maxOccupancy,
    this.bedType,
    this.size,
    required this.sizeUnit,
    this.pricePerNight,
    required this.currency,
    required this.available,
  });

  factory RoomTypeData.fromJson(Map<String, dynamic> json) {
    return RoomTypeData(
      name: json['name'] ?? '',
      description: json['description'],
      maxOccupancy: json['maxOccupancy'] ?? 2,
      bedType: json['bedType'],
      size: json['size'],
      sizeUnit: json['sizeUnit'] ?? 'sqm',
      pricePerNight: json['pricePerNight']?.toDouble(),
      currency: json['currency'] ?? 'EUR',
      available: json['available'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'maxOccupancy': maxOccupancy,
      'bedType': bedType,
      'size': size,
      'sizeUnit': sizeUnit,
      'pricePerNight': pricePerNight,
      'currency': currency,
      'available': available,
    };
  }
}

class ContactData {
  final String? phone;
  final String? email;
  final String? website;
  final String? whatsapp;
  final String? address;

  ContactData({
    this.phone,
    this.email,
    this.website,
    this.whatsapp,
    this.address,
  });

  factory ContactData.fromJson(Map<String, dynamic> json) {
    return ContactData(
      phone: json['phone'],
      email: json['email'],
      website: json['website'],
      whatsapp: json['whatsapp'],
      address: json['address'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'email': email,
      'website': website,
      'whatsapp': whatsapp,
      'address': address,
    };
  }
}

class PolicyData {
  final String title;
  final String description;
  final String type;
  final bool important;

  PolicyData({
    required this.title,
    required this.description,
    required this.type,
    required this.important,
  });

  factory PolicyData.fromJson(Map<String, dynamic> json) {
    return PolicyData(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: json['type'] ?? 'General',
      important: json['important'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'type': type,
      'important': important,
    };
  }
}
