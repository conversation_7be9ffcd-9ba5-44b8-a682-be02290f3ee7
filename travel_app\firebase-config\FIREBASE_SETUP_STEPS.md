# 🔥 Firebase Setup for UpAlbania - Step by Step

## 📋 **What You Need:**
- Google account
- 15 minutes of time
- The package name: `com.upalb.travel_app`

## 🚀 **Step 1: Create Firebase Project**

1. **Go to Firebase Console:**
   - Visit: https://console.firebase.google.com
   - Click "Create a project"

2. **Project Details:**
   ```
   Project name: UpAlbania Travel App
   Project ID: upalb-travel-app (or auto-generated)
   Location: Europe (for Albanian users)
   ```

3. **Google Analytics:**
   - Enable Google Analytics: ✅ YES
   - Choose existing account or create new
   - Location: Europe

## 📱 **Step 2: Add Android App**

1. **Register App:**
   ```
   Android package name: com.upalb.travel_app
   App nickname: UpAlbania Android
   Debug signing certificate: (leave empty for now)
   ```

2. **Download Config:**
   - Download `google-services.json`
   - Place in: `travel_app/android/app/google-services.json`

3. **Verify File Location:**
   ```
   travel_app/
   └── android/
       └── app/
           ├── google-services.json  ← HERE
           └── build.gradle
   ```

## 🍎 **Step 3: Add iOS App**

1. **Register App:**
   ```
   iOS bundle ID: com.upalb.travel_app
   App nickname: UpAlbania iOS
   App Store ID: (leave empty)
   ```

2. **Download Config:**
   - Download `GoogleService-Info.plist`
   - Place in: `travel_app/ios/Runner/GoogleService-Info.plist`

3. **Verify File Location:**
   ```
   travel_app/
   └── ios/
       └── Runner/
           ├── GoogleService-Info.plist  ← HERE
           └── Info.plist
   ```

## 🔐 **Step 4: Setup Authentication**

1. **Go to Authentication:**
   - In Firebase Console → Authentication
   - Click "Get started"

2. **Enable Sign-in Methods:**
   ```
   ✅ Email/Password
   ✅ Google (optional but recommended)
   ❌ Anonymous (optional)
   ```

3. **Configure Email/Password:**
   - Enable "Email/Password"
   - Enable "Email link (passwordless sign-in)" ← Optional

4. **Configure Google Sign-In (Optional):**
   - Enable "Google"
   - Project support email: <EMAIL>

## 💾 **Step 5: Setup Firestore Database**

1. **Create Database:**
   - Go to Firestore Database
   - Click "Create database"

2. **Security Rules:**
   ```
   Start in: Production mode
   Location: europe-west3 (Frankfurt)
   ```

3. **Apply Security Rules:**
   - Go to Rules tab
   - Copy content from `firebase-config/firestore.rules`
   - Click "Publish"

## 📁 **Step 6: Setup Storage**

1. **Create Storage:**
   - Go to Storage
   - Click "Get started"

2. **Security Rules:**
   ```
   Start in: Production mode
   Location: europe-west3 (Frankfurt)
   ```

3. **Apply Storage Rules:**
   - Go to Rules tab
   - Copy content from `firebase-config/storage.rules`
   - Click "Publish"

## 🔧 **Step 7: Get Your Configuration**

### **Firebase Project Settings:**
1. Go to Project Settings (gear icon)
2. Note down these values:

```javascript
// Your Firebase Config (for reference)
const firebaseConfig = {
  apiKey: "AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "upalb-travel-app-xxxxx.firebaseapp.com",
  projectId: "upalb-travel-app-xxxxx",
  storageBucket: "upalb-travel-app-xxxxx.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdefghijklmnop"
};
```

### **What the App Uses:**
- ✅ **Project ID:** For Firestore connection
- ✅ **Storage Bucket:** For file uploads
- ✅ **Auth Domain:** For authentication
- ✅ **API Keys:** Embedded in config files

## ✅ **Step 8: Verify Setup**

### **Check Files Exist:**
```bash
# Android config
ls travel_app/android/app/google-services.json

# iOS config  
ls travel_app/ios/Runner/GoogleService-Info.plist

# Should both exist and not be empty
```

### **Test Connection:**
```bash
cd travel_app
flutter run

# Look for in console:
# ✅ "Firebase initialized successfully"
# ✅ "Connected to Firestore"
# ❌ No Firebase errors
```

## 🎯 **What Your App Gets:**

### **User Authentication:**
- ✅ Sign up with email/password
- ✅ Sign in with email/password
- ✅ Password reset (email)
- ✅ User session management
- ✅ Secure user profiles

### **Data Storage:**
- ✅ User profiles and preferences
- ✅ Booking history
- ✅ Favorite destinations
- ✅ Reviews and ratings
- ✅ Travel history

### **File Storage:**
- ✅ Profile photos
- ✅ Review images
- ✅ Destination photos
- ✅ User uploads

### **Real-time Features:**
- ✅ Live booking updates
- ✅ Real-time reviews
- ✅ Instant favorites sync
- ✅ Cross-device synchronization

## 🚨 **Common Issues & Solutions:**

### **"google-services.json not found":**
```bash
# Make sure file is in correct location:
travel_app/android/app/google-services.json

# NOT in:
travel_app/android/google-services.json  ❌
travel_app/google-services.json          ❌
```

### **"Firebase project not found":**
- Check project ID in config files
- Verify project exists in Firebase Console
- Make sure you're using the right Google account

### **"Permission denied" errors:**
- Check Firestore security rules
- Verify user is authenticated
- Check user permissions

### **iOS build issues:**
- Make sure GoogleService-Info.plist is added to Xcode project
- Check bundle ID matches Firebase config
- Clean and rebuild iOS project

## 🎉 **You're Done!**

Your Firebase is now configured for:
- ✅ **User authentication** (sign up/in)
- ✅ **Data storage** (user profiles, bookings)
- ✅ **File uploads** (photos, documents)
- ✅ **Real-time sync** (cross-device)
- ✅ **Offline support** (cached data)

The app will automatically connect to Firebase when you run it! 🚀
