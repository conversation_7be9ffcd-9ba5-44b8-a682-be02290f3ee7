const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';

async function createDemoUser() {
  try {
    console.log('🔧 Creating demo admin user...');
    
    // Demo admin credentials
    const demoAdmin = {
      firstname: 'Demo',
      lastname: 'Admin',
      username: 'demo-admin',
      email: '<EMAIL>',
      password: 'DemoPass123!',
      confirmPassword: 'DemoPass123!'
    };
    
    // Try to register the admin user
    const response = await axios.post(`${STRAPI_URL}/admin/register-admin`, demoAdmin);
    
    if (response.status === 201) {
      console.log('✅ Demo admin user created successfully!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: DemoPass123!');
      console.log('🌐 Admin Panel: http://localhost:1337/admin');
    }
    
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('ℹ️ Admin user already exists or registration is closed');
      console.log('📧 Try logging in with: <EMAIL>');
      console.log('🔑 Password: DemoPass123!');
    } else {
      console.error('❌ Error creating demo user:', error.response?.data || error.message);
    }
  }
}

createDemoUser();
