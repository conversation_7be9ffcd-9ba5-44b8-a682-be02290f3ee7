{"collectionName": "components_travel_amenities", "info": {"displayName": "Amenity", "description": "Hotel or accommodation amenity"}, "options": {}, "attributes": {"name": {"type": "string", "required": true}, "icon": {"type": "string"}, "description": {"type": "text"}, "category": {"type": "enumeration", "enum": ["Room Features", "Bathroom", "Technology", "Food & Drink", "Recreation", "Business", "Accessibility", "Transportation", "General"], "default": "General"}, "available": {"type": "boolean", "default": true}, "additionalCost": {"type": "boolean", "default": false}}}