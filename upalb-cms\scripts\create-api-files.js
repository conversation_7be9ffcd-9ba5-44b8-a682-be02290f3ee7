const fs = require('fs');
const path = require('path');

const contentTypes = ['hotel', 'restaurant', 'activity', 'travel-tip', 'category'];

const controllerTemplate = (name) => `/**
 * ${name} controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::${name}.${name}');`;

const routeTemplate = (name) => `/**
 * ${name} router
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreRouter('api::${name}.${name}');`;

const serviceTemplate = (name) => `/**
 * ${name} service
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreService('api::${name}.${name}');`;

function createAPIFiles() {
  console.log('🔧 Creating missing API files...');
  
  contentTypes.forEach(contentType => {
    const apiDir = path.join(__dirname, '..', 'src', 'api', contentType);
    
    // Create directories
    const controllersDir = path.join(apiDir, 'controllers');
    const routesDir = path.join(apiDir, 'routes');
    const servicesDir = path.join(apiDir, 'services');
    
    [controllersDir, routesDir, servicesDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    // Create files
    const controllerFile = path.join(controllersDir, `${contentType}.ts`);
    const routeFile = path.join(routesDir, `${contentType}.ts`);
    const serviceFile = path.join(servicesDir, `${contentType}.ts`);
    
    if (!fs.existsSync(controllerFile)) {
      fs.writeFileSync(controllerFile, controllerTemplate(contentType));
      console.log(`✅ Created controller for ${contentType}`);
    }
    
    if (!fs.existsSync(routeFile)) {
      fs.writeFileSync(routeFile, routeTemplate(contentType));
      console.log(`✅ Created route for ${contentType}`);
    }
    
    if (!fs.existsSync(serviceFile)) {
      fs.writeFileSync(serviceFile, serviceTemplate(contentType));
      console.log(`✅ Created service for ${contentType}`);
    }
  });
  
  console.log('🎉 API files creation completed!');
}

createAPIFiles();
