import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ItinerariesScreen extends StatefulWidget {
  const ItinerariesScreen({super.key});

  @override
  State<ItinerariesScreen> createState() => _ItinerariesScreenState();
}

class _ItinerariesScreenState extends State<ItinerariesScreen> {
  String selectedDuration = 'All';
  
  final List<String> durations = ['All', '3 Days', '5 Days', '7 Days', '10+ Days'];
  
  final List<Map<String, dynamic>> itineraries = [
    {
      'id': '1',
      'title': 'Albanian Riviera Explorer',
      'duration': '7 Days',
      'price': 899,
      'rating': 4.9,
      'imageUrl': 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800',
      'highlights': ['Saranda', 'Butrint', 'Ksamil', 'Blue Eye'],
      'description': 'Discover the stunning Albanian coastline with crystal clear waters and ancient history.',
      'color': const Color(0xFF00A9F6),
    },
    {
      'id': '2',
      'title': 'Mountain Adventure Trek',
      'duration': '5 Days',
      'price': 649,
      'rating': 4.8,
      'imageUrl': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
      'highlights': ['Valbona Valley', 'Theth', 'Albanian Alps', 'Komani Lake'],
      'description': 'Experience the breathtaking Albanian Alps and pristine mountain villages.',
      'color': const Color(0xFF00F69C),
    },
    {
      'id': '3',
      'title': 'Cultural Heritage Journey',
      'duration': '10 Days',
      'price': 1299,
      'rating': 4.7,
      'imageUrl': 'https://images.unsplash.com/photo-1539066834-3fa5463c3d1c?w=800',
      'highlights': ['Tirana', 'Berat', 'Gjirokaster', 'Kruja'],
      'description': 'Immerse yourself in Albania\'s rich history and UNESCO World Heritage sites.',
      'color': const Color(0xFFFADC36),
    },
    {
      'id': '4',
      'title': 'Culinary & Wine Experience',
      'duration': '3 Days',
      'price': 449,
      'rating': 4.6,
      'imageUrl': 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=800',
      'highlights': ['Traditional Cooking', 'Wine Tasting', 'Local Markets', 'Farm Visits'],
      'description': 'Taste authentic Albanian cuisine and discover local culinary traditions.',
      'color': const Color(0xFFFF6B6B),
    },
    {
      'id': '5',
      'title': 'Adventure & Nature Combo',
      'duration': '7 Days',
      'price': 799,
      'rating': 4.8,
      'imageUrl': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
      'highlights': ['Rafting', 'Hiking', 'Cave Exploration', 'Wildlife Watching'],
      'description': 'Perfect blend of adventure activities and natural wonders of Albania.',
      'color': const Color(0xFFFBB73F),
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredItineraries = selectedDuration == 'All' 
        ? itineraries 
        : itineraries.where((item) => item['duration'] == selectedDuration).toList();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // Header
            SliverAppBar(
              expandedHeight: 140,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              elevation: 0,
              flexibleSpace: FlexibleSpaceBar(
                title: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'UpAlbania',
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF00CFC8),
                      ),
                    ),
                    Text(
                      'Curated Itineraries',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
              ),
              actions: [
                Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF00CFC8).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.filter_list_rounded,
                      color: Color(0xFF00CFC8),
                    ),
                    onPressed: () {},
                  ),
                ),
              ],
            ),
            
            // Duration Filter
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Filter by Duration',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 40,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: durations.length,
                        itemBuilder: (context, index) {
                          final duration = durations[index];
                          final isSelected = selectedDuration == duration;
                          return Padding(
                            padding: EdgeInsets.only(
                              right: index == durations.length - 1 ? 0 : 12,
                            ),
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedDuration = duration;
                                });
                              },
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 10,
                                ),
                                decoration: BoxDecoration(
                                  color: isSelected 
                                      ? const Color(0xFF00CFC8) 
                                      : Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: isSelected 
                                        ? const Color(0xFF00CFC8) 
                                        : Colors.grey[300]!,
                                    width: 1.5,
                                  ),
                                  boxShadow: isSelected ? [
                                    BoxShadow(
                                      color: const Color(0xFF00CFC8).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ] : null,
                                ),
                                child: Text(
                                  duration,
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: isSelected ? Colors.white : Colors.grey[700],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Itineraries List
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(
                        bottom: index == filteredItineraries.length - 1 ? 100 : 20,
                      ),
                      child: _buildItineraryCard(filteredItineraries[index]),
                    );
                  },
                  childCount: filteredItineraries.length,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItineraryCard(Map<String, dynamic> itinerary) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image and Duration Badge
          Stack(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                child: Image.network(
                  itinerary['imageUrl'],
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 200,
                      color: Colors.grey[300],
                      child: const Icon(Icons.error),
                    );
                  },
                ),
              ),
              Positioned(
                top: 16,
                left: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: itinerary['color'],
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: itinerary['color'].withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    itinerary['duration'],
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.favorite_border_rounded,
                    color: Colors.black87,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        itinerary['title'],
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.amber[50],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star_rounded,
                            size: 16,
                            color: Colors.amber[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            itinerary['rating'].toString(),
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.amber[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                Text(
                  itinerary['description'],
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.5,
                  ),
                ),

                const SizedBox(height: 16),

                // Highlights
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: (itinerary['highlights'] as List<String>).map((highlight) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: const Color(0xFF00CFC8).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        highlight,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF00CFC8),
                        ),
                      ),
                    );
                  }).toList(),
                ),

                const SizedBox(height: 20),

                // Price and Book Button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Starting from',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          '\$${itinerary['price']}',
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF00CFC8),
                          ),
                        ),
                      ],
                    ),
                    ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF00CFC8),
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        'Book Now',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
