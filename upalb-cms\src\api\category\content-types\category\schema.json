{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": "Categories for organizing travel content"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "text"}, "icon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "color": {"type": "string", "description": "Hex color code for the category"}, "type": {"type": "enumeration", "enum": ["Destination", "Activity", "Accommodation", "Dining", "Transportation", "General"], "required": true}, "isActive": {"type": "boolean", "default": true}, "sortOrder": {"type": "integer", "default": 0}, "parentCategory": {"type": "relation", "relation": "manyToOne", "target": "api::category.category", "inversedBy": "subCategories"}, "subCategories": {"type": "relation", "relation": "oneToMany", "target": "api::category.category", "mappedBy": "parentCategory"}}}