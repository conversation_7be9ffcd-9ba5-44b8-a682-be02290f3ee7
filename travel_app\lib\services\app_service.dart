import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'firebase_service.dart';
import 'database_service.dart';
import 'hybrid_sync_service.dart';
import '../models/user_model.dart';
import '../models/cms_model.dart';

class AppService {
  static final AppService _instance = AppService._internal();
  factory AppService() => _instance;
  AppService._internal();

  final FirebaseService _firebaseService = FirebaseService();
  final DatabaseService _databaseService = DatabaseService();
  final HybridSyncService _hybridSyncService = HybridSyncService();

  // Stream controllers for real-time updates
  final StreamController<UserModel?> _userController = StreamController<UserModel?>.broadcast();
  final StreamController<List<DestinationPost>> _destinationsController = StreamController<List<DestinationPost>>.broadcast();
  final StreamController<List<CMSPost>> _postsController = StreamController<List<CMSPost>>.broadcast();
  final StreamController<bool> _loadingController = StreamController<bool>.broadcast();
  final StreamController<String?> _errorController = StreamController<String?>.broadcast();

  // Public streams
  Stream<UserModel?> get userStream => _userController.stream;
  Stream<List<DestinationPost>> get destinationsStream => _destinationsController.stream;
  Stream<List<CMSPost>> get postsStream => _postsController.stream;
  Stream<bool> get loadingStream => _loadingController.stream;
  Stream<String?> get errorStream => _errorController.stream;

  // Current state
  UserModel? _currentUser;
  bool _isInitialized = false;
  bool _isOnline = true;

  UserModel? get currentUser => _currentUser;
  bool get isInitialized => _isInitialized;
  bool get isOnline => _isOnline;

  // Initialize the app service
  Future<void> initialize() async {
    try {
      print('🚀 Starting AppService initialization...');
      _setLoading(true);

      // Initialize database (skip on web platform)
      if (!kIsWeb) {
        print('📦 Initializing database...');
        await _databaseService.database;
        print('✅ Database initialized');

        // Initialize hybrid sync service
        print('🔄 Initializing hybrid sync service...');
        await _hybridSyncService.initialize();
        print('✅ Hybrid sync service initialized');
      } else {
        print('🌐 Web platform - skipping database and sync service initialization');
      }

      // Check connectivity
      print('🌐 Checking connectivity...');
      final connectivity = Connectivity();
      final connectivityResult = await connectivity.checkConnectivity();
      _isOnline = connectivityResult != ConnectivityResult.none;
      print('✅ Connectivity status: ${_isOnline ? "Online" : "Offline"}');

      // Listen to connectivity changes
      connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
        _isOnline = result != ConnectivityResult.none;
        print('🌐 Connectivity changed: ${_isOnline ? "Online" : "Offline"}');
      });

      // Listen to auth state changes
      print('🔐 Setting up auth state listener...');
      FirebaseAuth.instance.authStateChanges().listen(_onAuthStateChanged);
      print('✅ Auth state listener set up');

      // Load initial content with timeout
      print('📱 Loading initial content...');
      await _loadInitialContent().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          print('⚠️ Initial content loading timed out, continuing without it');
        },
      );
      print('✅ Initial content loaded (or timed out)');

      _isInitialized = true;
      _setLoading(false);
      print('🎉 AppService initialization completed successfully!');
    } catch (e) {
      print('❌ AppService initialization failed: $e');
      _setError('Failed to initialize app: $e');
      _setLoading(false);
    }
  }

  // Authentication methods
  Future<UserModel?> signInWithGoogle() async {
    try {
      _setLoading(true);
      _setError(null);

      print('🔐 Attempting Google sign in...');
      final userCredential = await _firebaseService.signInWithGoogle();

      if (userCredential?.user != null) {
        print('✅ Google sign in successful: ${userCredential!.user!.email}');
        return _currentUser; // Will be set by auth state listener
      } else {
        print('❌ Google sign in failed or cancelled');
        _setError('Google sign in failed or was cancelled');
        return null;
      }
    } catch (e) {
      print('❌ Google sign in error: $e');
      _setError('Google sign in failed: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<UserModel?> signInWithEmail(String email, String password) async {
    try {
      _setLoading(true);
      _setError(null);

      print('🔐 Attempting email sign in...');
      final userCredential = await _firebaseService.signInWithEmail(email, password);
      if (userCredential != null) {
        print('✅ Email sign in successful: ${userCredential.user!.email}');

        // Wait for the auth state listener to process the user
        // This ensures _currentUser is set before we return
        int attempts = 0;
        while (_currentUser == null && attempts < 50) { // Wait up to 5 seconds
          await Future.delayed(const Duration(milliseconds: 100));
          attempts++;
        }

        _setLoading(false);
        if (_currentUser != null) {
          print('✅ User state updated by auth listener: ${_currentUser!.name}');
          return _currentUser;
        } else {
          print('⚠️ Auth listener did not update user state in time');
          return null;
        }
      }

      print('❌ Email sign in failed: No user credential returned');
      _setLoading(false);
      return null;
    } catch (e) {
      print('❌ AppService sign in error: $e');
      _setError('Sign in failed: $e');
      _setLoading(false);
      return null;
    }
  }

  Future<UserModel?> signUpWithEmail(String email, String password, String name) async {
    try {
      _setLoading(true);
      _setError(null);

      print('🔐 Attempting email sign up...');
      final userCredential = await _firebaseService.signUpWithEmail(email, password, name);
      if (userCredential != null) {
        print('✅ Email sign up successful: ${userCredential.user!.email}');

        // Wait for the auth state listener to process the user
        // This ensures _currentUser is set before we return
        int attempts = 0;
        while (_currentUser == null && attempts < 50) { // Wait up to 5 seconds
          await Future.delayed(const Duration(milliseconds: 100));
          attempts++;
        }

        _setLoading(false);
        if (_currentUser != null) {
          print('✅ User state updated by auth listener: ${_currentUser!.name}');
          return _currentUser;
        } else {
          print('⚠️ Auth listener did not update user state in time');
          return null;
        }
      }

      print('❌ Email sign up failed: No user credential returned');
      _setLoading(false);
      return null;
    } catch (e) {
      print('❌ AppService sign up error: $e');
      _setError('Sign up failed: $e');
      _setLoading(false);
      return null;
    }
  }

  Future<void> signOut() async {
    try {
      print('🔐 Starting sign out process...');
      // Use Firebase service to sign out (this handles both Firebase Auth and Google Sign-In)
      await _firebaseService.signOut();
      print('✅ Firebase sign out completed');

      // Clear current user state
      _currentUser = null;
      _userController.add(null);
      print('🔄 User state cleared and stream updated');
    } catch (e) {
      print('❌ Sign out error: $e');
      _setError('Sign out failed: $e');
    }
  }

  /// Clear any authentication errors and reset state
  /// Useful when switching between login/signup screens
  void clearAuthState() {
    print('🔄 Clearing auth state...');
    _setError(null);
    _setLoading(false);
  }

  // Content methods
  Future<List<DestinationPost>> getDestinations({
    String? region,
    String? category,
    int limit = 20,
    bool forceRefresh = false,
  }) async {
    try {
      if (forceRefresh || _isOnline) {
        _setLoading(true);
      }
      
      final destinations = await _hybridSyncService.getDestinations(
        region: region,
        category: category,
        limit: limit,
      );
      
      _destinationsController.add(destinations);
      _setLoading(false);
      return destinations;
    } catch (e) {
      _setError('Failed to load destinations: $e');
      _setLoading(false);
      return [];
    }
  }

  Future<List<CMSPost>> getPosts({
    String? category,
    int limit = 10,
    bool forceRefresh = false,
  }) async {
    try {
      if (forceRefresh || _isOnline) {
        _setLoading(true);
      }
      
      final posts = await _hybridSyncService.getPosts(
        category: category,
        limit: limit,
      );
      
      _postsController.add(posts);
      _setLoading(false);
      return posts;
    } catch (e) {
      _setError('Failed to load posts: $e');
      _setLoading(false);
      return [];
    }
  }

  Future<CMSPost?> getPostBySlug(String slug) async {
    try {
      _setLoading(true);
      final post = await _hybridSyncService.getPostBySlug(slug);
      _setLoading(false);
      return post;
    } catch (e) {
      _setError('Failed to load post: $e');
      _setLoading(false);
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> searchContent(String query) async {
    try {
      _setLoading(true);
      final results = await _hybridSyncService.searchContent(query);
      _setLoading(false);
      return results;
    } catch (e) {
      _setError('Search failed: $e');
      _setLoading(false);
      return [];
    }
  }

  // User data methods
  Future<String> createBooking(Map<String, dynamic> bookingData) async {
    try {
      if (_currentUser == null) {
        throw Exception('User must be logged in to create booking');
      }
      
      bookingData['user_id'] = _currentUser!.id;
      final bookingId = await _hybridSyncService.createBooking(bookingData);
      
      return bookingId;
    } catch (e) {
      _setError('Failed to create booking: $e');
      rethrow;
    }
  }

  Future<List<BookingModel>> getUserBookings() async {
    try {
      if (_currentUser == null) {
        return [];
      }
      
      return await _hybridSyncService.getUserBookings(_currentUser!.id);
    } catch (e) {
      _setError('Failed to load bookings: $e');
      return [];
    }
  }

  Future<void> addToFavorites(String itemId, String itemType, {
    String? itemTitle,
    String? itemImage,
  }) async {
    try {
      if (_currentUser == null) {
        throw Exception('User must be logged in to add favorites');
      }
      
      await _hybridSyncService.addToFavorites(
        _currentUser!.id,
        itemId,
        itemType,
        itemTitle: itemTitle,
        itemImage: itemImage,
      );
    } catch (e) {
      _setError('Failed to add to favorites: $e');
      rethrow;
    }
  }

  Future<void> removeFromFavorites(String itemId, String itemType) async {
    try {
      if (_currentUser == null) {
        throw Exception('User must be logged in to remove favorites');
      }
      
      await _hybridSyncService.removeFromFavorites(
        _currentUser!.id,
        itemId,
        itemType,
      );
    } catch (e) {
      _setError('Failed to remove from favorites: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getUserFavorites(String itemType) async {
    try {
      if (_currentUser == null) {
        return [];
      }
      
      return await _hybridSyncService.getUserFavorites(_currentUser!.id, itemType);
    } catch (e) {
      _setError('Failed to load favorites: $e');
      return [];
    }
  }

  Future<bool> isFavorite(String itemId, String itemType) async {
    try {
      if (_currentUser == null) {
        return false;
      }
      
      final favorites = await getUserFavorites(itemType);
      return favorites.any((fav) => fav['item_id'] == itemId);
    } catch (e) {
      return false;
    }
  }

  // Content sync methods
  Future<void> syncContent() async {
    try {
      if (!_isOnline) {
        _setError('Cannot sync content while offline');
        return;
      }
      
      _setLoading(true);
      await _hybridSyncService.syncContentFromCMS();
      
      // Refresh current content
      await _loadInitialContent();
      
      _setLoading(false);
    } catch (e) {
      _setError('Content sync failed: $e');
      _setLoading(false);
    }
  }

  // Private methods
  void _onAuthStateChanged(User? user) async {
    print('🔥 Auth state changed: ${user != null ? "User signed in (${user.email})" : "User signed out"}');
    if (user != null) {
      // User signed in
      try {
        var userData = await _firebaseService.getUserProfile(user.uid);

        // If user profile doesn't exist, create one (this should only happen for Google sign-in users)
        if (userData == null) {
          print('📝 Creating user profile for authenticated user: ${user.email}');
          print('⚠️ This should only happen for Google sign-in users, not email/password users');
          userData = {
            'name': user.displayName ?? user.email?.split('@')[0] ?? 'User',
            'email': user.email ?? '',
            'profileImage': user.photoURL ?? '',
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'preferences': {
              'language': 'en',
              'currency': 'EUR',
              'notifications': true,
            },
            'travelStats': {
              'tripsCompleted': 0,
              'visitedCountries': [],
              'reviewsWritten': 0,
              'totalDistance': 0,
            },
          };
          await _firebaseService.createUserProfile(user.uid, userData);
        } else {
          print('✅ Found existing user profile: ${userData['name']} (${userData['email']})');
        }

        _currentUser = UserModel.fromMap(user.uid, userData);
        print('🔄 Adding user to stream: ${_currentUser?.name} (${_currentUser?.email})');
        _userController.add(_currentUser);

        // Cache user locally
        await _databaseService.cacheUser(_currentUser!);
      } catch (e) {
        print('❌ Error in auth state change: $e');
        // Try to load from local cache
        _currentUser = await _databaseService.getCachedUser(user.uid);
        _userController.add(_currentUser);
      }
    } else {
      // User signed out
      print('❌ Setting user to null in auth state change');
      _currentUser = null;
      _userController.add(null);
    }
  }

  Future<void> _loadInitialContent() async {
    try {
      print('📍 Loading destinations...');
      // Load featured destinations with timeout
      await getDestinations(limit: 10).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          print('⚠️ Destinations loading timed out');
          return <DestinationPost>[];
        },
      );
      print('✅ Destinations loaded');

      print('📝 Loading posts...');
      // Load latest posts with timeout
      await getPosts(limit: 10).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          print('⚠️ Posts loading timed out');
          return <CMSPost>[];
        },
      );
      print('✅ Posts loaded');

      // Content is automatically added to streams in the methods above
    } catch (e) {
      print('❌ Error loading initial content: $e');
      // Don't rethrow - we want the app to continue even if content loading fails
    }
  }

  void _setLoading(bool loading) {
    _loadingController.add(loading);
  }

  void _setError(String? error) {
    _errorController.add(error);
  }

  // Cleanup
  void dispose() {
    _userController.close();
    _destinationsController.close();
    _postsController.close();
    _loadingController.close();
    _errorController.close();
    _hybridSyncService.dispose();
  }
}


