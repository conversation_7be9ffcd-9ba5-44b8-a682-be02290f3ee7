{"kind": "collectionType", "collectionName": "blog_posts", "info": {"singularName": "blog-post", "pluralName": "blog-posts", "displayName": "Blog Post", "description": "Travel blog posts and articles"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "title", "required": true}, "excerpt": {"type": "text", "maxLength": 500, "pluginOptions": {"i18n": {"localized": true}}}, "content": {"type": "richtext", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "featuredImage": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "gallery": {"type": "media", "multiple": true, "allowedTypes": ["images", "videos"]}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::category.category", "inversedBy": "blog_posts"}, "tags": {"type": "relation", "relation": "manyToMany", "target": "api::tag.tag", "mappedBy": "blog_posts"}, "author": {"type": "relation", "relation": "manyToOne", "target": "api::author.author", "inversedBy": "blog_posts"}, "readTime": {"type": "integer", "min": 1, "default": 5}, "featured": {"type": "boolean", "default": false}, "trending": {"type": "boolean", "default": false}, "viewCount": {"type": "integer", "default": 0}, "likeCount": {"type": "integer", "default": 0}, "relatedDestinations": {"type": "relation", "relation": "manyToMany", "target": "api::destination.destination"}, "relatedPosts": {"type": "relation", "relation": "manyToMany", "target": "api::blog-post.blog-post"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "publishedAt": {"type": "datetime"}}}