import 'hotel_model.dart'; // For shared classes like LocationData, ContactData

class RestaurantModel {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String excerpt;
  final String? featuredImage;
  final List<String> gallery;
  final LocationData? location;
  final String cuisine;
  final String priceRange;
  final PricingData? averagePrice;
  final List<OpeningHoursData> openingHours;
  final ContactData? contactInfo;
  final List<SpecialtyData> specialties;
  final List<MenuSectionData> menu;
  final List<FeatureData> features;
  final String region;
  final String city;
  final String country;
  final double rating;
  final int reviewCount;
  final bool featured;
  final bool reservationRequired;
  final String? reservationUrl;
  final String? website;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;

  RestaurantModel({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.excerpt,
    this.featuredImage,
    this.gallery = const [],
    this.location,
    required this.cuisine,
    required this.priceRange,
    this.averagePrice,
    this.openingHours = const [],
    this.contactInfo,
    this.specialties = const [],
    this.menu = const [],
    this.features = const [],
    required this.region,
    required this.city,
    required this.country,
    required this.rating,
    required this.reviewCount,
    required this.featured,
    required this.reservationRequired,
    this.reservationUrl,
    this.website,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory RestaurantModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? {};
    
    return RestaurantModel(
      id: json['id'] ?? 0,
      name: attributes['name'] ?? '',
      slug: attributes['slug'] ?? '',
      description: attributes['description'] ?? '',
      excerpt: attributes['excerpt'] ?? '',
      featuredImage: _extractImageUrl(attributes['featuredImage']),
      gallery: _extractGalleryUrls(attributes['gallery']),
      location: attributes['location'] != null 
          ? LocationData.fromJson(attributes['location']) 
          : null,
      cuisine: attributes['cuisine'] ?? 'International',
      priceRange: attributes['priceRange'] ?? 'Mid-range',
      averagePrice: attributes['averagePrice'] != null 
          ? PricingData.fromJson(attributes['averagePrice']) 
          : null,
      openingHours: _extractOpeningHours(attributes['openingHours']),
      contactInfo: attributes['contactInfo'] != null 
          ? ContactData.fromJson(attributes['contactInfo']) 
          : null,
      specialties: _extractSpecialties(attributes['specialties']),
      menu: _extractMenu(attributes['menu']),
      features: _extractFeatures(attributes['features']),
      region: attributes['region'] ?? 'Central Albania',
      city: attributes['city'] ?? '',
      country: attributes['country'] ?? 'Albania',
      rating: (attributes['rating'] ?? 0.0).toDouble(),
      reviewCount: attributes['reviewCount'] ?? 0,
      featured: attributes['featured'] ?? false,
      reservationRequired: attributes['reservationRequired'] ?? false,
      reservationUrl: attributes['reservationUrl'],
      website: attributes['website'],
      tags: _extractTags(attributes['tags']),
      createdAt: DateTime.tryParse(attributes['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(attributes['updatedAt'] ?? '') ?? DateTime.now(),
    );
  }

  static String? _extractImageUrl(dynamic imageData) {
    if (imageData == null) return null;
    if (imageData['data'] == null) return null;
    return imageData['data']['attributes']?['url'];
  }

  static List<String> _extractGalleryUrls(dynamic galleryData) {
    if (galleryData == null || galleryData['data'] == null) return [];
    final List<dynamic> images = galleryData['data'] ?? [];
    return images
        .map((img) => img['attributes']?['url'] as String?)
        .where((url) => url != null)
        .cast<String>()
        .toList();
  }

  static List<OpeningHoursData> _extractOpeningHours(dynamic hoursData) {
    if (hoursData == null) return [];
    final List<dynamic> hours = hoursData is List ? hoursData : [];
    return hours.map((hour) => OpeningHoursData.fromJson(hour)).toList();
  }

  static List<SpecialtyData> _extractSpecialties(dynamic specialtiesData) {
    if (specialtiesData == null) return [];
    final List<dynamic> specialties = specialtiesData is List ? specialtiesData : [];
    return specialties.map((specialty) => SpecialtyData.fromJson(specialty)).toList();
  }

  static List<MenuSectionData> _extractMenu(dynamic menuData) {
    if (menuData == null) return [];
    final List<dynamic> menu = menuData is List ? menuData : [];
    return menu.map((section) => MenuSectionData.fromJson(section)).toList();
  }

  static List<FeatureData> _extractFeatures(dynamic featuresData) {
    if (featuresData == null) return [];
    final List<dynamic> features = featuresData is List ? featuresData : [];
    return features.map((feature) => FeatureData.fromJson(feature)).toList();
  }

  static List<String> _extractTags(dynamic tagsData) {
    if (tagsData == null || tagsData['data'] == null) return [];
    final List<dynamic> tags = tagsData['data'] ?? [];
    return tags
        .map((tag) => tag['attributes']?['name'] as String?)
        .where((name) => name != null)
        .cast<String>()
        .toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'excerpt': excerpt,
      'featuredImage': featuredImage,
      'gallery': gallery,
      'location': location?.toJson(),
      'cuisine': cuisine,
      'priceRange': priceRange,
      'averagePrice': averagePrice?.toJson(),
      'openingHours': openingHours.map((h) => h.toJson()).toList(),
      'contactInfo': contactInfo?.toJson(),
      'specialties': specialties.map((s) => s.toJson()).toList(),
      'menu': menu.map((m) => m.toJson()).toList(),
      'features': features.map((f) => f.toJson()).toList(),
      'region': region,
      'city': city,
      'country': country,
      'rating': rating,
      'reviewCount': reviewCount,
      'featured': featured,
      'reservationRequired': reservationRequired,
      'reservationUrl': reservationUrl,
      'website': website,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class OpeningHoursData {
  final String day;
  final String? openTime;
  final String? closeTime;
  final bool closed;
  final String? notes;

  OpeningHoursData({
    required this.day,
    this.openTime,
    this.closeTime,
    required this.closed,
    this.notes,
  });

  factory OpeningHoursData.fromJson(Map<String, dynamic> json) {
    return OpeningHoursData(
      day: json['day'] ?? '',
      openTime: json['openTime'],
      closeTime: json['closeTime'],
      closed: json['closed'] ?? false,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'openTime': openTime,
      'closeTime': closeTime,
      'closed': closed,
      'notes': notes,
    };
  }
}

class SpecialtyData {
  final String name;
  final String? description;
  final double? price;
  final String currency;
  final String? image;
  final List<String> ingredients;
  final List<String> allergens;
  final String dietary;
  final int spicyLevel;
  final bool popular;

  SpecialtyData({
    required this.name,
    this.description,
    this.price,
    required this.currency,
    this.image,
    this.ingredients = const [],
    this.allergens = const [],
    required this.dietary,
    required this.spicyLevel,
    required this.popular,
  });

  factory SpecialtyData.fromJson(Map<String, dynamic> json) {
    return SpecialtyData(
      name: json['name'] ?? '',
      description: json['description'],
      price: json['price']?.toDouble(),
      currency: json['currency'] ?? 'EUR',
      image: json['image']?['data']?['attributes']?['url'],
      ingredients: List<String>.from(json['ingredients'] ?? []),
      allergens: List<String>.from(json['allergens'] ?? []),
      dietary: json['dietary'] ?? 'None',
      spicyLevel: json['spicyLevel'] ?? 0,
      popular: json['popular'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'image': image,
      'ingredients': ingredients,
      'allergens': allergens,
      'dietary': dietary,
      'spicyLevel': spicyLevel,
      'popular': popular,
    };
  }
}

class MenuSectionData {
  final String name;
  final String? description;
  final int order;
  final List<MenuItemData> items;

  MenuSectionData({
    required this.name,
    this.description,
    required this.order,
    this.items = const [],
  });

  factory MenuSectionData.fromJson(Map<String, dynamic> json) {
    return MenuSectionData(
      name: json['name'] ?? '',
      description: json['description'],
      order: json['order'] ?? 1,
      items: _extractMenuItems(json['items']),
    );
  }

  static List<MenuItemData> _extractMenuItems(dynamic itemsData) {
    if (itemsData == null) return [];
    final List<dynamic> items = itemsData is List ? itemsData : [];
    return items.map((item) => MenuItemData.fromJson(item)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'order': order,
      'items': items.map((i) => i.toJson()).toList(),
    };
  }
}

class MenuItemData {
  final String name;
  final String? description;
  final double price;
  final String currency;
  final String? image;
  final List<String> ingredients;
  final List<String> allergens;
  final String dietary;
  final int spicyLevel;
  final bool available;
  final bool popular;

  MenuItemData({
    required this.name,
    this.description,
    required this.price,
    required this.currency,
    this.image,
    this.ingredients = const [],
    this.allergens = const [],
    required this.dietary,
    required this.spicyLevel,
    required this.available,
    required this.popular,
  });

  factory MenuItemData.fromJson(Map<String, dynamic> json) {
    return MenuItemData(
      name: json['name'] ?? '',
      description: json['description'],
      price: (json['price'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'EUR',
      image: json['image']?['data']?['attributes']?['url'],
      ingredients: List<String>.from(json['ingredients'] ?? []),
      allergens: List<String>.from(json['allergens'] ?? []),
      dietary: json['dietary'] ?? 'None',
      spicyLevel: json['spicyLevel'] ?? 0,
      available: json['available'] ?? true,
      popular: json['popular'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'image': image,
      'ingredients': ingredients,
      'allergens': allergens,
      'dietary': dietary,
      'spicyLevel': spicyLevel,
      'available': available,
      'popular': popular,
    };
  }
}

class FeatureData {
  final String name;
  final String? description;
  final String? icon;
  final String category;
  final bool available;

  FeatureData({
    required this.name,
    this.description,
    this.icon,
    required this.category,
    required this.available,
  });

  factory FeatureData.fromJson(Map<String, dynamic> json) {
    return FeatureData(
      name: json['name'] ?? '',
      description: json['description'],
      icon: json['icon'],
      category: json['category'] ?? 'General',
      available: json['available'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'icon': icon,
      'category': category,
      'available': available,
    };
  }
}
