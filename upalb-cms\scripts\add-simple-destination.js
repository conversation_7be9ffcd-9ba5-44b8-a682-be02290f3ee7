const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';

const api = axios.create({
  baseURL: STRAPI_URL,
  headers: {
    'Authorization': `Bearer ${API_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function addDestination() {
  try {
    const destinationData = {
      data: {
        name: "Tirana",
        slug: "tirana",
        description: "The vibrant capital city of Albania, known for its colorful buildings and rich history.",
        shortDescription: "Albania's colorful capital",
        region: "Central Albania",
        category: "City",
        latitude: 41.3275,
        longitude: 19.8187,
        isFeatured: true,
        isPopular: true,
        rating: 4.5,
        estimatedCost: 50,
        bestTimeToVisit: "April to October",
        duration: "2-3 days",
        publishedAt: new Date().toISOString()
      }
    };

    console.log('Adding destination:', JSON.stringify(destinationData, null, 2));

    const response = await api.post('/api/destinations', destinationData);
    console.log('✅ Destination added successfully!');
    console.log('Response:', response.data);
  } catch (error) {
    console.error('❌ Error adding destination:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

addDestination();
