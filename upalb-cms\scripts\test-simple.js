const axios = require('axios');

const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';
const STRAPI_URL = 'http://localhost:1337';

async function testSimple() {
  try {
    console.log('🔍 Testing simple API call...');
    
    // Test GET destinations
    console.log('\n1. Testing GET /api/destinations');
    try {
      const response = await axios.get(`${STRAPI_URL}/api/destinations`, {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`
        }
      });
      console.log('✅ GET destinations successful:', response.status);
      console.log('Data:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ GET destinations failed:', error.response?.status, error.response?.statusText);
      console.log('Error details:', error.response?.data);
    }
    
    // Test POST destinations with required data
    console.log('\n2. Testing POST /api/destinations');
    const minimalDestination = {
      data: {
        name: "Test Destination",
        slug: "test-destination",
        description: "A test destination",
        shortDescription: "Test",
        region: "Tirana Region",
        category: "City",
        difficulty: "Easy",
        bestTimeToVisit: ["Summer"],
        estimatedDuration: "1 day",
        entryFee: 0,
        currency: "ALL",
        isPopular: false,
        isFeatured: false,
        latitude: 41.0,
        longitude: 19.0,
        address: "Test Address",
        rating: 4.0,
        reviewCount: 0
      }
    };
    
    try {
      const response = await axios.post(`${STRAPI_URL}/api/destinations`, minimalDestination, {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ POST destinations successful:', response.status);
      console.log('Created:', response.data);
    } catch (error) {
      console.log('❌ POST destinations failed:', error.response?.status, error.response?.statusText);
      console.log('Error details:', JSON.stringify(error.response?.data, null, 2));
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testSimple();
