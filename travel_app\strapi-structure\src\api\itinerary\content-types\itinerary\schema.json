{"kind": "collectionType", "collectionName": "itineraries", "info": {"singularName": "itinerary", "pluralName": "itineraries", "displayName": "Travel Itinerary", "description": "Pre-planned travel packages and itineraries"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "title", "required": true}, "description": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "excerpt": {"type": "text", "maxLength": 300, "pluginOptions": {"i18n": {"localized": true}}}, "featuredImage": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "gallery": {"type": "media", "multiple": true, "allowedTypes": ["images", "videos"]}, "duration": {"type": "integer", "required": true, "min": 1}, "durationType": {"type": "enumeration", "enum": ["days", "hours"], "default": "days"}, "difficulty": {"type": "enumeration", "enum": ["Easy", "Moderate", "Challenging", "Expert"], "default": "Easy"}, "groupSize": {"type": "component", "repeatable": false, "component": "travel.group-size"}, "pricing": {"type": "component", "repeatable": false, "component": "travel.pricing"}, "destinations": {"type": "relation", "relation": "manyToMany", "target": "api::destination.destination"}, "dailyItinerary": {"type": "component", "repeatable": true, "component": "travel.daily-plan"}, "includes": {"type": "component", "repeatable": true, "component": "content.list-item"}, "excludes": {"type": "component", "repeatable": true, "component": "content.list-item"}, "requirements": {"type": "component", "repeatable": true, "component": "content.list-item"}, "highlights": {"type": "component", "repeatable": true, "component": "content.highlight"}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::category.category", "inversedBy": "itineraries"}, "tags": {"type": "relation", "relation": "manyToMany", "target": "api::tag.tag", "mappedBy": "itineraries"}, "bestTimeToTravel": {"type": "component", "repeatable": true, "component": "travel.season"}, "transportation": {"type": "component", "repeatable": false, "component": "travel.transportation"}, "accommodation": {"type": "component", "repeatable": true, "component": "travel.accommodation"}, "rating": {"type": "decimal", "min": 0, "max": 5, "default": 0}, "reviewCount": {"type": "integer", "default": 0}, "bookingCount": {"type": "integer", "default": 0}, "featured": {"type": "boolean", "default": false}, "popular": {"type": "boolean", "default": false}, "availability": {"type": "component", "repeatable": false, "component": "travel.availability"}, "cancellationPolicy": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}}}