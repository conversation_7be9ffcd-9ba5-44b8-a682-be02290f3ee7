import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String name;
  final String email;
  final String profileImage;
  final UserPreferences preferences;
  final TravelStats travelStats;
  final DateTime createdAt;
  final DateTime? updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.profileImage,
    required this.preferences,
    required this.travelStats,
    required this.createdAt,
    this.updatedAt,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      profileImage: data['profileImage'] ?? '',
      preferences: UserPreferences.fromMap(data['preferences'] ?? {}),
      travelStats: TravelStats.fromMap(data['travelStats'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  factory UserModel.fromMap(String id, Map<String, dynamic> data) {
    return UserModel(
      id: id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      profileImage: data['profileImage'] ?? '',
      preferences: UserPreferences.fromMap(data['preferences'] ?? {}),
      travelStats: TravelStats.fromMap(data['travelStats'] ?? {}),
      createdAt: data['createdAt'] is Timestamp
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] is Timestamp
              ? (data['updatedAt'] as Timestamp).toDate()
              : DateTime.now())
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'profileImage': profileImage,
      'preferences': preferences.toMap(),
      'travelStats': travelStats.toMap(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  UserModel copyWith({
    String? name,
    String? email,
    String? profileImage,
    UserPreferences? preferences,
    TravelStats? travelStats,
  }) {
    return UserModel(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImage: profileImage ?? this.profileImage,
      preferences: preferences ?? this.preferences,
      travelStats: travelStats ?? this.travelStats,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }
}

class UserPreferences {
  final String language;
  final String currency;
  final bool notifications;
  final bool emailUpdates;
  final String theme;

  UserPreferences({
    required this.language,
    required this.currency,
    required this.notifications,
    this.emailUpdates = true,
    this.theme = 'light',
  });

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      language: map['language'] ?? 'en',
      currency: map['currency'] ?? 'EUR',
      notifications: map['notifications'] ?? true,
      emailUpdates: map['emailUpdates'] ?? true,
      theme: map['theme'] ?? 'light',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'language': language,
      'currency': currency,
      'notifications': notifications,
      'emailUpdates': emailUpdates,
      'theme': theme,
    };
  }
}

class TravelStats {
  final int tripsCompleted;
  final int placesVisited;
  final double totalSpent;
  final int reviewsWritten;
  final List<String> visitedCountries;

  TravelStats({
    required this.tripsCompleted,
    required this.placesVisited,
    required this.totalSpent,
    this.reviewsWritten = 0,
    this.visitedCountries = const [],
  });

  factory TravelStats.fromMap(Map<String, dynamic> map) {
    return TravelStats(
      tripsCompleted: map['tripsCompleted'] ?? 0,
      placesVisited: map['placesVisited'] ?? 0,
      totalSpent: (map['totalSpent'] ?? 0.0).toDouble(),
      reviewsWritten: map['reviewsWritten'] ?? 0,
      visitedCountries: List<String>.from(map['visitedCountries'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'tripsCompleted': tripsCompleted,
      'placesVisited': placesVisited,
      'totalSpent': totalSpent,
      'reviewsWritten': reviewsWritten,
      'visitedCountries': visitedCountries,
    };
  }
}

class BookingModel {
  final String id;
  final String userId;
  final String itineraryId;
  final String itineraryTitle;
  final String status; // 'pending', 'confirmed', 'completed', 'cancelled'
  final DateTime startDate;
  final DateTime endDate;
  final double totalPrice;
  final int travelers;
  final Map<String, dynamic> travelerDetails;
  final DateTime createdAt;
  final DateTime updatedAt;

  BookingModel({
    required this.id,
    required this.userId,
    required this.itineraryId,
    required this.itineraryTitle,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.totalPrice,
    required this.travelers,
    required this.travelerDetails,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BookingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return BookingModel(
      id: doc.id,
      userId: data['userId'],
      itineraryId: data['itineraryId'],
      itineraryTitle: data['itineraryTitle'],
      status: data['status'],
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      totalPrice: (data['totalPrice'] ?? 0.0).toDouble(),
      travelers: data['travelers'] ?? 1,
      travelerDetails: Map<String, dynamic>.from(data['travelerDetails'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'itineraryId': itineraryId,
      'itineraryTitle': itineraryTitle,
      'status': status,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'totalPrice': totalPrice,
      'travelers': travelers,
      'travelerDetails': travelerDetails,
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }
}

class ReviewModel {
  final String id;
  final String userId;
  final String userName;
  final String userImage;
  final String itemId;
  final String itemType; // 'destination', 'itinerary', 'hotel'
  final double rating;
  final String comment;
  final List<String> images;
  final DateTime createdAt;

  ReviewModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userImage,
    required this.itemId,
    required this.itemType,
    required this.rating,
    required this.comment,
    required this.images,
    required this.createdAt,
  });

  factory ReviewModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ReviewModel(
      id: doc.id,
      userId: data['userId'],
      userName: data['userName'],
      userImage: data['userImage'] ?? '',
      itemId: data['itemId'],
      itemType: data['itemType'],
      rating: (data['rating'] ?? 0.0).toDouble(),
      comment: data['comment'] ?? '',
      images: List<String>.from(data['images'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'itemId': itemId,
      'itemType': itemType,
      'rating': rating,
      'comment': comment,
      'images': images,
      'createdAt': FieldValue.serverTimestamp(),
    };
  }
}
