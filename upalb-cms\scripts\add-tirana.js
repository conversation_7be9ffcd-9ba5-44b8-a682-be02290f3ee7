const axios = require('axios');

const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';
const BASE_URL = 'http://localhost:1337';

const headers = {
  'Authorization': `Bearer ${API_TOKEN}`,
  'Content-Type': 'application/json',
};

async function addTirana() {
  try {
    const destinationData = {
      data: {
        name: "Tirana",
        slug: "tirana",
        description: "The vibrant capital city of Albania, where modern life meets rich history. Tirana offers colorful buildings, bustling cafes, and a lively cultural scene.",
        shortDescription: "Albania's colorful capital city",
        region: "Central Albania",
        category: "City",
        latitude: 41.3275,
        longitude: 19.8187,
        imageUrl: "https://example.com/tirana.jpg",
        isRecommended: true,
        isFeatured: true,
        rating: 4.5,
        reviewCount: 150,
        bestTimeToVisit: "April to October",
        averageStayDuration: 2,
        priceLevel: "Medium",
        tags: ["capital", "culture", "nightlife", "history"],
        nearbyAttractions: ["Skanderbeg Square", "Et'hem Bey Mosque", "National History Museum"],
        publishedAt: new Date().toISOString()
      }
    };

    console.log('Adding Tirana destination...');
    console.log('Data:', JSON.stringify(destinationData, null, 2));

    const response = await axios.post(
      `${BASE_URL}/api/destinations`,
      destinationData,
      { headers }
    );

    console.log('✅ Success! Destination added:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Error adding destination:');
    console.error('Status:', error.response?.status);
    console.error('Message:', error.response?.data?.error?.message);
    console.error('Details:', JSON.stringify(error.response?.data, null, 2));
  }
}

addTirana();
