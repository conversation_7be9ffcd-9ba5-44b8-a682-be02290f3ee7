import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/app_service.dart';
import '../models/user_model.dart';
import 'auth/login_screen.dart';

class ProfileScreen extends StatelessWidget {
  final AppService? appService;

  const ProfileScreen({super.key, this.appService});

  @override
  Widget build(BuildContext context) {
    final service = appService ?? AppService();
    print('🔍 ProfileScreen - appService: ${appService != null ? "provided" : "null, creating new"}');
    print('🔍 ProfileScreen - service initialized: ${service.isInitialized}');
    print('🔍 ProfileScreen - service.currentUser: ${service.currentUser?.name ?? "null"} (${service.currentUser?.email ?? "no email"})');

    return Scaffold(
      body: SafeArea(
        child: StreamBuilder<UserModel?>(
          stream: service.userStream,
          initialData: service.currentUser, // Use current user as initial data
          builder: (context, snapshot) {
            final user = snapshot.data;
            print('🔍 ProfileScreen - StreamBuilder called: hasData=${snapshot.hasData}, user: ${user?.name ?? "null"} (${user?.email ?? "no email"})');

            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Profile',
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2E7D8A).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.settings,
                      color: Color(0xFF2E7D8A),
                      size: 20,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Profile Picture and Info
              Column(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: user?.profileImage.isNotEmpty == true
                        ? null
                        : const LinearGradient(
                            colors: [Color(0xFF2E7D8A), Color(0xFF4A9BA8)],
                          ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF2E7D8A).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                      image: user?.profileImage.isNotEmpty == true
                        ? DecorationImage(
                            image: NetworkImage(user!.profileImage),
                            fit: BoxFit.cover,
                          )
                        : null,
                    ),
                    child: user?.profileImage.isNotEmpty == true
                      ? null
                      : const Icon(
                          Icons.person,
                          size: 50,
                          color: Colors.white,
                        ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    user?.name ?? 'Guest User',
                    style: GoogleFonts.poppins(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),

                  const SizedBox(height: 4),

                  Text(
                    user?.email ?? 'Not logged in',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  if (user != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7D8A).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        'Member since ${_formatDate(user.createdAt)}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2E7D8A),
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Stats Row
              if (user != null)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStatCard('${user.travelStats.tripsCompleted}', 'Trips'),
                    _buildStatCard('${user.travelStats.visitedCountries.length}', 'Countries'),
                    _buildStatCard('${user.travelStats.reviewsWritten}', 'Reviews'),
                  ],
                ),
              
              const SizedBox(height: 32),
              
              // Menu Items
              _buildMenuItem(
                Icons.person_outline,
                'Personal Information',
                'Update your details and preferences',
                () {
                  _showPersonalInfoDialog(context, user, service);
                },
              ),

              _buildMenuItem(
                Icons.payment_outlined,
                'Payment Methods',
                'Manage your payment options',
                () {
                  _showPaymentMethodsDialog(context);
                },
              ),

              _buildMenuItem(
                Icons.notifications_outlined,
                'Notifications',
                'Customize your notification settings',
                () {
                  _showNotificationSettingsDialog(context);
                },
              ),

              _buildMenuItem(
                Icons.security_outlined,
                'Security',
                'Password and security settings',
                () {
                  _showSecuritySettingsDialog(context);
                },
              ),

              _buildMenuItem(
                Icons.help_outline,
                'Help & Support',
                'Get help and contact support',
                () {
                  _showHelpSupportDialog(context);
                },
              ),

              _buildMenuItem(
                Icons.info_outline,
                'About',
                'App version and information',
                () {
                  _showAboutDialog(context);
                },
              ),
              
              const SizedBox(height: 32),
              
              // Logout Button
              if (user != null)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      await service.signOut();
                      if (context.mounted) {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => LoginScreen(appService: service),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[400],
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Logout',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

              // Login Button for guests
              if (user == null)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder: (context) => LoginScreen(appService: service),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7D8A),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Login',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatCard(String value, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2E7D8A),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ListTile(
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 8,
        ),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF2E7D8A).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: const Color(0xFF2E7D8A),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  void _showPersonalInfoDialog(BuildContext context, UserModel? user, AppService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Personal Information',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Name: ${user?.name ?? 'Not set'}'),
            const SizedBox(height: 8),
            Text('Email: ${user?.email ?? 'Not set'}'),
            const SizedBox(height: 8),
            Text('Member since: ${user?.createdAt ?? 'Unknown'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPaymentMethodsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Payment Methods',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: const Text('Payment methods management coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Notification Settings',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: const Text('Notification settings coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSecuritySettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Security Settings',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: const Text('Security settings coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelpSupportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Help & Support',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Need help? Contact us:'),
            SizedBox(height: 8),
            Text('Email: <EMAIL>'),
            Text('Phone: +355 69 123 4567'),
            SizedBox(height: 8),
            Text('Or visit our FAQ section in the app.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'About UpAlbania',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('UpAlbania Travel App'),
            SizedBox(height: 8),
            Text('Version: 1.0.0'),
            Text('Build: 2024.1'),
            SizedBox(height: 8),
            Text('Discover the beauty of Albania with our comprehensive travel guide.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
