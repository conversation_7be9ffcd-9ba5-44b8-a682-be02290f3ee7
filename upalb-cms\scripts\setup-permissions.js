const axios = require('axios');

const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';
const STRAPI_URL = 'http://localhost:1337';

async function setupPermissions() {
  try {
    console.log('🔧 Setting up API permissions...');
    
    // Get current permissions
    console.log('1. Getting current permissions...');
    const permissionsResponse = await axios.get(`${STRAPI_URL}/admin/content-api/permissions`, {
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`
      }
    });
    
    console.log('✅ Permissions retrieved');
    console.log('Available sections:', Object.keys(permissionsResponse.data.data.sections));
    
    // Get routes
    console.log('2. Getting available routes...');
    const routesResponse = await axios.get(`${STRAPI_URL}/admin/content-api/routes`, {
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`
      }
    });
    
    console.log('✅ Routes retrieved');
    console.log('Available routes:', routesResponse.data.data.length);
    
    // List some routes for debugging
    routesResponse.data.data.slice(0, 10).forEach(route => {
      console.log(`  - ${route.method} ${route.path}`);
    });
    
  } catch (error) {
    console.error('❌ Error setting up permissions:', error.response?.status, error.response?.statusText);
    console.error('Error details:', error.response?.data);
  }
}

setupPermissions();
