const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';

// Configure axios with authentication
const api = axios.create({
  baseURL: STRAPI_URL,
  headers: {
    'Authorization': `Bearer ${API_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Sample Albanian destinations
const destinations = [
  {
    name: "Tirana",
    slug: "tirana",
    description: "The vibrant capital city of Albania, known for its colorful buildings, bustling nightlife, and rich history. Explore Skanderbeg Square, the National History Museum, and the colorful Blloku district.",
    shortDescription: "Albania's colorful capital with vibrant culture and history",
    region: "Tirana Region",
    category: "City",
    difficulty: "Easy",
    bestTimeToVisit: ["Spring", "Summer", "Autumn"],
    estimatedDuration: "2-3 days",
    entryFee: 0,
    currency: "ALL",
    isPopular: true,
    isFeatured: true,
    latitude: 41.3275,
    longitude: 19.8187,
    address: "Tirana, Albania",
    weatherInfo: "Mediterranean climate with hot summers and mild winters",
    transportationInfo: "Well connected by bus, taxi, and rental cars. International airport nearby.",
    localLanguage: "Albanian",
    emergencyContacts: "Police: 129, Fire: 128, Medical: 127",
    rating: 4.5,
    reviewCount: 1250
  },
  {
    name: "Saranda",
    slug: "saranda",
    description: "A beautiful coastal city in southern Albania, gateway to the Albanian Riviera with stunning beaches and crystal-clear waters. Perfect base for exploring Butrint National Park and nearby Greek islands.",
    shortDescription: "Gateway to Albanian Riviera with beautiful beaches",
    region: "Southern Albania",
    category: "Beach",
    difficulty: "Easy",
    bestTimeToVisit: ["Summer", "Spring", "Autumn"],
    estimatedDuration: "3-5 days",
    entryFee: 0,
    currency: "ALL",
    isPopular: true,
    isFeatured: true,
    latitude: 39.8759,
    longitude: 20.0106,
    address: "Saranda, Albania",
    weatherInfo: "Mediterranean climate, perfect for beach activities",
    transportationInfo: "Accessible by bus from Tirana, ferry connections to Corfu",
    localLanguage: "Albanian",
    emergencyContacts: "Police: 129, Fire: 128, Medical: 127",
    rating: 4.7,
    reviewCount: 890
  },
  {
    name: "Gjirokaster",
    slug: "gjirokaster",
    description: "UNESCO World Heritage site known as the 'City of Stone', featuring Ottoman architecture and rich cultural heritage. Visit the imposing castle and explore the old bazaar.",
    shortDescription: "UNESCO World Heritage 'City of Stone' with Ottoman architecture",
    region: "Southern Albania",
    category: "UNESCO Site",
    difficulty: "Moderate",
    bestTimeToVisit: ["Spring", "Summer", "Autumn"],
    estimatedDuration: "1-2 days",
    entryFee: 0,
    currency: "ALL",
    isPopular: true,
    isFeatured: true,
    latitude: 40.0758,
    longitude: 20.1389,
    address: "Gjirokaster, Albania",
    weatherInfo: "Continental climate with hot summers",
    transportationInfo: "Accessible by bus from Tirana and Saranda",
    localLanguage: "Albanian",
    emergencyContacts: "Police: 129, Fire: 128, Medical: 127",
    rating: 4.6,
    reviewCount: 567
  },
  {
    name: "Berat",
    slug: "berat",
    description: "The 'City of a Thousand Windows', another UNESCO World Heritage site famous for its well-preserved Ottoman architecture and historic castle.",
    shortDescription: "UNESCO site known as 'City of a Thousand Windows'",
    region: "Central Albania",
    category: "UNESCO Site",
    difficulty: "Moderate",
    bestTimeToVisit: ["Spring", "Summer", "Autumn"],
    estimatedDuration: "1-2 days",
    entryFee: 0,
    currency: "ALL",
    isPopular: true,
    isFeatured: true,
    latitude: 40.7058,
    longitude: 19.9522,
    address: "Berat, Albania",
    weatherInfo: "Mediterranean climate with warm summers",
    transportationInfo: "Accessible by bus from Tirana, about 2 hours drive",
    localLanguage: "Albanian",
    emergencyContacts: "Police: 129, Fire: 128, Medical: 127",
    rating: 4.5,
    reviewCount: 423
  },
  {
    name: "Valbona Valley",
    slug: "valbona-valley",
    description: "Stunning mountain valley in the Albanian Alps, perfect for hiking, nature photography, and experiencing traditional mountain life.",
    shortDescription: "Breathtaking valley in the Albanian Alps",
    region: "Albanian Alps",
    category: "Mountain",
    difficulty: "Challenging",
    bestTimeToVisit: ["Summer", "Spring", "Autumn"],
    estimatedDuration: "2-4 days",
    entryFee: 0,
    currency: "ALL",
    isPopular: true,
    isFeatured: true,
    latitude: 42.4167,
    longitude: 19.9167,
    address: "Valbona Valley, Albania",
    weatherInfo: "Mountain climate, cool summers and snowy winters",
    transportationInfo: "Accessible by car or bus to Valbona village, then hiking trails",
    localLanguage: "Albanian",
    emergencyContacts: "Police: 129, Fire: 128, Medical: 127",
    rating: 4.8,
    reviewCount: 234
  },
  {
    name: "Ksamil",
    slug: "ksamil",
    description: "Paradise beach destination with crystal-clear turquoise waters and small islands. Often called the 'Maldives of Albania'.",
    shortDescription: "Paradise beach known as 'Maldives of Albania'",
    region: "Southern Albania",
    category: "Beach",
    difficulty: "Easy",
    bestTimeToVisit: ["Summer", "Spring"],
    estimatedDuration: "2-3 days",
    entryFee: 0,
    currency: "ALL",
    isPopular: true,
    isFeatured: true,
    latitude: 39.7667,
    longitude: 20.0167,
    address: "Ksamil, Albania",
    weatherInfo: "Mediterranean climate, ideal for swimming",
    transportationInfo: "Close to Saranda, accessible by bus or car",
    localLanguage: "Albanian",
    emergencyContacts: "Police: 129, Fire: 128, Medical: 127",
    rating: 4.9,
    reviewCount: 678
  }
];

// Sample hotels
const hotels = [
  {
    name: "Tirana International Hotel & Conference Centre",
    slug: "tirana-international-hotel",
    description: "Luxury hotel in the heart of Tirana with modern amenities and conference facilities.",
    shortDescription: "Luxury hotel in central Tirana",
    category: "Luxury Hotel",
    starRating: 5,
    priceRange: "Luxury",
    pricePerNight: 150,
    currency: "ALL",
    latitude: 41.3275,
    longitude: 19.8187,
    address: "Skanderbeg Square, Tirana",
    phone: "+355 4 2274 185",
    email: "<EMAIL>",
    checkInTime: "15:00",
    checkOutTime: "11:00",
    website: "https://tiranainternational.com",
    isRecommended: true,
    isFeatured: true,
    rating: 4.5,
    reviewCount: 324
  },
  {
    name: "Hotel Butrint",
    slug: "hotel-butrint-saranda",
    description: "Comfortable beachfront hotel in Saranda with sea views and easy access to Butrint National Park.",
    shortDescription: "Beachfront hotel with sea views in Saranda",
    category: "Beach Resort",
    starRating: 4,
    priceRange: "Mid-range",
    pricePerNight: 80,
    currency: "ALL",
    latitude: 39.8759,
    longitude: 20.0106,
    address: "Saranda Waterfront, Saranda",
    phone: "+355 85 123 456",
    email: "<EMAIL>",
    checkInTime: "14:00",
    checkOutTime: "12:00",
    website: "https://hotelbutrint.com",
    isRecommended: true,
    isFeatured: false,
    rating: 4.3,
    reviewCount: 189
  },
  {
    name: "Guesthouse Ksamil",
    slug: "guesthouse-ksamil",
    description: "Family-run guesthouse near the beautiful Ksamil beaches, offering authentic Albanian hospitality.",
    shortDescription: "Family guesthouse near Ksamil beaches",
    category: "Guesthouse",
    starRating: 3,
    priceRange: "Budget",
    pricePerNight: 35,
    currency: "ALL",
    latitude: 39.7667,
    longitude: 20.0167,
    address: "Ksamil Village, Albania",
    phone: "+355 69 987 654",
    email: "<EMAIL>",
    checkInTime: "14:00",
    checkOutTime: "11:00",
    website: "",
    isRecommended: true,
    isFeatured: false,
    rating: 4.1,
    reviewCount: 67
  }
];

// Sample restaurants
const restaurants = [
  {
    name: "Mullixhiu",
    slug: "mullixhiu",
    description: "Award-winning restaurant serving modern Albanian cuisine with locally sourced ingredients.",
    shortDescription: "Award-winning modern Albanian cuisine",
    cuisine: ["Albanian Traditional", "Mediterranean"],
    priceRange: "Fine Dining",
    averagePrice: 45,
    currency: "ALL",
    latitude: 41.3275,
    longitude: 19.8187,
    address: "Tirana, Albania",
    phone: "+355 69 123 4567",
    email: "<EMAIL>",
    specialties: "Traditional Albanian dishes with modern twist",
    atmosphere: ["Modern", "Romantic"],
    isRecommended: true,
    isFeatured: true,
    hasDelivery: false,
    acceptsReservations: true,
    rating: 4.8,
    reviewCount: 156
  },
  {
    name: "Taverna Kuka",
    slug: "taverna-kuka",
    description: "Traditional Albanian taverna in Gjirokaster serving authentic local dishes in a historic setting.",
    shortDescription: "Traditional taverna in historic Gjirokaster",
    cuisine: ["Albanian Traditional"],
    priceRange: "Mid-range",
    averagePrice: 25,
    currency: "ALL",
    latitude: 40.0758,
    longitude: 20.1389,
    address: "Old Bazaar, Gjirokaster",
    phone: "+355 84 567 890",
    email: "<EMAIL>",
    specialties: "Qifqi, Byrek, Traditional meat dishes",
    atmosphere: ["Traditional", "Family-friendly"],
    isRecommended: true,
    isFeatured: false,
    hasDelivery: false,
    acceptsReservations: true,
    rating: 4.4,
    reviewCount: 89
  },
  {
    name: "Limani Restaurant",
    slug: "limani-restaurant-saranda",
    description: "Seafood restaurant on Saranda waterfront with fresh catch of the day and beautiful sea views.",
    shortDescription: "Waterfront seafood restaurant in Saranda",
    cuisine: ["Seafood", "Mediterranean"],
    priceRange: "Mid-range",
    averagePrice: 30,
    currency: "ALL",
    latitude: 39.8759,
    longitude: 20.0106,
    address: "Saranda Waterfront, Saranda",
    phone: "+355 85 234 567",
    email: "<EMAIL>",
    specialties: "Fresh fish, seafood platters, grilled octopus",
    atmosphere: ["Outdoor", "Romantic"],
    isRecommended: true,
    isFeatured: true,
    hasDelivery: false,
    acceptsReservations: true,
    rating: 4.6,
    reviewCount: 234
  },
  {
    name: "Oda Restaurant",
    slug: "oda-restaurant-tirana",
    description: "Cozy restaurant in Tirana serving traditional Albanian comfort food in a warm, welcoming atmosphere.",
    shortDescription: "Traditional Albanian comfort food in Tirana",
    cuisine: ["Albanian Traditional"],
    priceRange: "Budget",
    averagePrice: 18,
    currency: "ALL",
    latitude: 41.3275,
    longitude: 19.8187,
    address: "Blloku District, Tirana",
    phone: "+355 4 456 789",
    email: "<EMAIL>",
    specialties: "Tavë kosi, Fërgesë, Traditional soups",
    atmosphere: ["Casual", "Family-friendly"],
    isRecommended: true,
    isFeatured: false,
    hasDelivery: true,
    acceptsReservations: false,
    rating: 4.2,
    reviewCount: 145
  }
];

// Sample activities
const activities = [
  {
    name: "Butrint National Park Tour",
    slug: "butrint-national-park-tour",
    description: "Explore the ancient ruins of Butrint, a UNESCO World Heritage site with Greek, Roman, and Byzantine remains.",
    shortDescription: "UNESCO archaeological site tour",
    category: ["Historical", "Cultural"],
    difficulty: "Easy",
    duration: "Half day (4 hours)",
    price: 25,
    currency: "ALL",
    priceIncludes: "Guide, entrance fees, transportation from Saranda",
    priceExcludes: "Lunch, personal expenses",
    minParticipants: 2,
    maxParticipants: 15,
    ageRestriction: "All ages welcome",
    requirements: "Comfortable walking shoes, sun protection",
    whatToBring: "Water bottle, camera, hat",
    meetingPoint: "Saranda city center",
    latitude: 39.7747,
    longitude: 20.0208,
    address: "Butrint National Park, near Saranda",
    phone: "+355 85 345 678",
    email: "<EMAIL>",
    bookingUrl: "https://butrinttours.com/book",
    isRecommended: true,
    isFeatured: true,
    isAvailableYearRound: true,
    bestTimeToVisit: ["Spring", "Summer", "Autumn"],
    rating: 4.7,
    reviewCount: 189
  },
  {
    name: "Albanian Alps Hiking",
    slug: "albanian-alps-hiking",
    description: "Multi-day hiking adventure through the stunning Albanian Alps, including Valbona Valley and Theth.",
    shortDescription: "Multi-day Alps hiking adventure",
    category: ["Adventure", "Nature", "Hiking"],
    difficulty: "Challenging",
    duration: "3 days",
    price: 180,
    currency: "ALL",
    priceIncludes: "Guide, accommodation, meals, transportation",
    priceExcludes: "Personal gear, insurance",
    minParticipants: 4,
    maxParticipants: 8,
    ageRestriction: "16+ years",
    requirements: "Good physical condition, hiking experience",
    whatToBring: "Hiking boots, backpack, warm clothes, sleeping bag",
    meetingPoint: "Shkoder city center",
    latitude: 42.4167,
    longitude: 19.9167,
    address: "Valbona Valley, Albanian Alps",
    phone: "+355 69 456 789",
    email: "<EMAIL>",
    bookingUrl: "https://alpshiking.com/book",
    isRecommended: true,
    isFeatured: true,
    isAvailableYearRound: false,
    bestTimeToVisit: ["Summer", "Spring"],
    rating: 4.9,
    reviewCount: 67
  }
];

// Sample travel tips
const travelTips = [
  {
    title: "Albanian Currency and Money Tips",
    slug: "albanian-currency-money-tips",
    content: "The Albanian Lek (ALL) is the official currency. ATMs are widely available in cities. Credit cards are accepted in most hotels and restaurants in tourist areas, but carry cash for small businesses and rural areas.",
    excerpt: "Essential money and currency information for traveling in Albania",
    category: "Money & Budget",
    isGeneral: true,
    isFeatured: true,
    priority: "High",
    author: "UpAlbania Team",
    readingTime: 3
  },
  {
    title: "Best Time to Visit Albania",
    slug: "best-time-visit-albania",
    content: "Albania has a Mediterranean climate. Summer (June-August) is perfect for beaches but can be crowded. Spring (April-May) and autumn (September-October) offer pleasant weather and fewer crowds. Winter is ideal for mountain activities.",
    excerpt: "When to visit Albania for the best weather and experiences",
    category: "Weather",
    isGeneral: true,
    isFeatured: true,
    priority: "High",
    author: "UpAlbania Team",
    readingTime: 2
  },
  {
    title: "Albanian Language Basics",
    slug: "albanian-language-basics",
    content: "Albanian is the official language. English is spoken in tourist areas, especially by younger people. Learn basic phrases: 'Faleminderit' (Thank you), 'Mirëdita' (Good day), 'Sa kushton?' (How much does it cost?)",
    excerpt: "Essential Albanian phrases for travelers",
    category: "Language",
    isGeneral: true,
    isFeatured: false,
    priority: "Medium",
    author: "UpAlbania Team",
    readingTime: 4
  }
];

async function seedData() {
  try {
    console.log('🌱 Starting to seed UpAlbania travel data...');
    
    // Create destinations
    console.log('📍 Creating destinations...');
    for (const destination of destinations) {
      try {
        const response = await api.post('/api/destinations', {
          data: destination
        });
        console.log(`✅ Created destination: ${destination.name}`);
      } catch (error) {
        console.error(`❌ Error creating destination ${destination.name}:`, error.response?.data || error.message);
      }
    }
    
    // Create hotels
    console.log('🏨 Creating hotels...');
    for (const hotel of hotels) {
      try {
        const response = await api.post('/api/hotels', {
          data: hotel
        });
        console.log(`✅ Created hotel: ${hotel.name}`);
      } catch (error) {
        console.error(`❌ Error creating hotel ${hotel.name}:`, error.response?.data || error.message);
      }
    }
    
    // Create restaurants
    console.log('🍽️ Creating restaurants...');
    for (const restaurant of restaurants) {
      try {
        const response = await api.post('/api/restaurants', {
          data: restaurant
        });
        console.log(`✅ Created restaurant: ${restaurant.name}`);
      } catch (error) {
        console.error(`❌ Error creating restaurant ${restaurant.name}:`, error.response?.data || error.message);
      }
    }

    // Create activities
    console.log('🎯 Creating activities...');
    for (const activity of activities) {
      try {
        const response = await api.post('/api/activities', {
          data: activity
        });
        console.log(`✅ Created activity: ${activity.name}`);
      } catch (error) {
        console.error(`❌ Error creating activity ${activity.name}:`, error.response?.data || error.message);
      }
    }

    // Create travel tips
    console.log('💡 Creating travel tips...');
    for (const tip of travelTips) {
      try {
        const response = await api.post('/api/travel-tips', {
          data: tip
        });
        console.log(`✅ Created travel tip: ${tip.title}`);
      } catch (error) {
        console.error(`❌ Error creating travel tip ${tip.title}:`, error.response?.data || error.message);
      }
    }

    console.log('🎉 Seeding completed!');

  } catch (error) {
    console.error('💥 Seeding failed:', error.message);
  }
}

// Run the seeding
seedData();
