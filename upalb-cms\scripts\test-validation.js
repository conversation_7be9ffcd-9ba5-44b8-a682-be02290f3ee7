const axios = require('axios');

const API_TOKEN = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';
const STRAPI_URL = 'http://localhost:1337';

const api = axios.create({
  baseURL: STRAPI_URL,
  headers: {
    'Authorization': `Bearer ${API_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function testValidation() {
  try {
    console.log('🔍 Testing validation with minimal destination...');
    
    const minimalDestination = {
      data: {
        name: "Test Destination 123",
        slug: "test-destination-123",
        description: "A test destination for validation",
        region: "Tirana Region",
        category: "City"
      }
    };
    
    const response = await api.post('/api/destinations', minimalDestination);
    console.log('✅ Success! Created destination:', response.data);
    
  } catch (error) {
    console.log('❌ Validation failed:', error.response?.status);
    console.log('Error details:', JSON.stringify(error.response?.data, null, 2));
    
    if (error.response?.data?.error?.details?.errors) {
      console.log('\n📋 Specific validation errors:');
      error.response.data.error.details.errors.forEach((err, index) => {
        console.log(`${index + 1}. Field: ${err.path?.join('.')}`);
        console.log(`   Message: ${err.message}`);
        console.log(`   Value: ${err.value}`);
      });
    }
  }
}

testValidation();
