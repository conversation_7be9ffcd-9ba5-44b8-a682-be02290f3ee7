{"collectionName": "components_shared_seos", "info": {"displayName": "SEO", "description": "Search Engine Optimization metadata"}, "options": {}, "attributes": {"metaTitle": {"type": "string", "maxLength": 60}, "metaDescription": {"type": "text", "maxLength": 160}, "keywords": {"type": "string"}, "canonicalUrl": {"type": "string"}, "ogTitle": {"type": "string", "maxLength": 60}, "ogDescription": {"type": "text", "maxLength": 160}, "ogImage": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "ogType": {"type": "enumeration", "enum": ["website", "article", "product"], "default": "article"}, "twitterCard": {"type": "enumeration", "enum": ["summary", "summary_large_image", "app", "player"], "default": "summary_large_image"}, "twitterTitle": {"type": "string", "maxLength": 70}, "twitterDescription": {"type": "text", "maxLength": 200}, "twitterImage": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "structuredData": {"type": "json"}, "robots": {"type": "enumeration", "enum": ["index,follow", "noindex,follow", "index,nofollow", "noindex,nofollow"], "default": "index,follow"}}}