import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/destination_model.dart';
import '../config/app_config.dart';

class StrapiService {
  static String get baseUrl => AppConfig.strapiMedia;
  static const String apiToken = '907b2436cb4b2c35adff708a2bb4be2666ce3c013c302253e2092474f618a948ad151e13314990262bd9fe92d8bbc7ed229c95d4ef070b69c29a950405e78e143ca2e5add32309a6b03a759d8643030df07f137046d390d175aa73648011654d44bd051373ba520a5128b9923a6194b5a446b6bb623f2cf3236e6c4d52136152';

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $apiToken',
    'Content-Type': 'application/json',
  };

  // Get all destinations
  static Future<List<DestinationModel>> getDestinations() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.strapiUrl}/destinations?populate=*'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> destinationsJson = data['data'] ?? [];

        return destinationsJson
            .map((json) => DestinationModel.fromJson(json))
            .toList();
      } else {
        print('Failed to load destinations: ${response.statusCode}');
        print('Response body: ${response.body}');
        return [];
      }
    } catch (e) {
      print('Error fetching destinations: $e');
      return [];
    }
  }

  // Get featured destinations
  static Future<List<DestinationModel>> getFeaturedDestinations() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.strapiUrl}/destinations?filters[isFeatured][\$eq]=true&populate=*'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> destinationsJson = data['data'] ?? [];

        return destinationsJson
            .map((json) => DestinationModel.fromJson(json))
            .toList();
      } else {
        print('Failed to load featured destinations: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error fetching featured destinations: $e');
      return [];
    }
  }

  // Get popular destinations
  static Future<List<DestinationModel>> getPopularDestinations() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.strapiUrl}/destinations?filters[isPopular][\$eq]=true&populate=*'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> destinationsJson = data['data'] ?? [];

        return destinationsJson
            .map((json) => DestinationModel.fromJson(json))
            .toList();
      } else {
        print('Failed to load popular destinations: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error fetching popular destinations: $e');
      return [];
    }
  }

  // Get destination by ID
  static Future<DestinationModel?> getDestination(int id) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.strapiUrl}/destinations/$id?populate=*'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return DestinationModel.fromJson(data['data']);
      } else {
        print('Failed to load destination: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error fetching destination: $e');
      return null;
    }
  }

  // Search destinations
  static Future<List<DestinationModel>> searchDestinations(String query) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.strapiUrl}/destinations?filters[\$or][0][name][\$containsi]=$query&filters[\$or][1][description][\$containsi]=$query&populate=*'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> destinationsJson = data['data'] ?? [];

        return destinationsJson
            .map((json) => DestinationModel.fromJson(json))
            .toList();
      } else {
        print('Failed to search destinations: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error searching destinations: $e');
      return [];
    }
  }

  // Test connection
  static Future<bool> testConnection() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.strapiUrl}/destinations'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Connection test failed: $e');
      return false;
    }
  }

  // Get full media URL
  static String getMediaUrl(String path) {
    if (path.startsWith('http')) return path;
    return '$baseUrl$path';
  }
}


