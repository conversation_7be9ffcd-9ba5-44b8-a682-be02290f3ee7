const axios = require('axios');

async function checkServer() {
  try {
    console.log('🔍 Checking if Strapi server is running...');
    
    const response = await axios.get('http://localhost:1337/admin', {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ Strapi server is running!');
      console.log('Admin panel accessible at: http://localhost:1337/admin');
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Strapi server is not running');
      console.log('Please start the server with: npm run develop');
    } else {
      console.log('⚠️ Server check failed:', error.message);
    }
  }
}

checkServer();
