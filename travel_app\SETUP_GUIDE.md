# 🇦🇱 UpAlbania - Complete Setup Guide

## 📋 Overview
This guide will help you set up the complete UpAlbania travel app with Firebase database and Strapi CMS integration.

## 🔥 Firebase Setup

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Name it "UpAlbania" 
4. Enable Google Analytics (optional)

### 2. Enable Firebase Services
```bash
# In Firebase Console, enable:
- Authentication (Email/Password, Google)
- Firestore Database
- Storage
- Analytics (optional)
```

### 3. Add Flutter App to Firebase
1. Click "Add app" → Flutter
2. Register app with package name: `com.upalb.travel_app`
3. Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
4. Follow Firebase setup instructions

### 4. Firestore Database Structure
```javascript
// Collections to create in Firestore:

users/
├── {userId}/
    ├── name: string
    ├── email: string
    ├── profileImage: string
    ├── preferences: object
    ├── travelStats: object
    └── createdAt: timestamp

bookings/
├── {bookingId}/
    ├── userId: string
    ├── itineraryId: string
    ├── status: string
    ├── startDate: timestamp
    ├── endDate: timestamp
    ├── totalPrice: number
    └── travelers: number

favorites/
├── {favoriteId}/
    ├── userId: string
    ├── itemId: string
    ├── itemType: string
    └── createdAt: timestamp

reviews/
├── {reviewId}/
    ├── userId: string
    ├── itemId: string
    ├── rating: number
    ├── comment: string
    └── createdAt: timestamp

posts/ (synced from CMS)
├── {postId}/
    ├── title: string
    ├── content: string
    ├── category: string
    ├── published: boolean
    └── publishedAt: timestamp
```

## 🎨 Strapi CMS Setup

### 1. Install Strapi
```bash
# Create new Strapi project
npx create-strapi-app upalb-cms --quickstart

# Navigate to project
cd upalb-cms

# Start development server
npm run develop
```

### 2. Create Content Types

#### Destinations Content Type
```javascript
// In Strapi Admin Panel → Content-Types Builder

Fields:
- title (Text) - Required
- slug (UID) - Required, target: title
- description (Rich Text)
- excerpt (Text)
- featuredImage (Media - Single)
- gallery (Media - Multiple)
- location (JSON)
- highlights (Text - Multiple)
- category (Relation - Many to One with Categories)
- region (Text)
- country (Text) - Default: "Albania"
- published (Boolean) - Default: false
- publishedAt (DateTime)
```

#### Blog Posts Content Type
```javascript
Fields:
- title (Text) - Required
- slug (UID) - Required, target: title
- excerpt (Text)
- content (Rich Text) - Required
- featuredImage (Media - Single)
- gallery (Media - Multiple)
- category (Relation - Many to One with Categories)
- tags (Relation - Many to Many with Tags)
- author (Relation - Many to One with Authors)
- published (Boolean) - Default: false
- publishedAt (DateTime)
- seo (Component - Single)
```

#### Travel Guides Content Type
```javascript
Fields:
- title (Text) - Required
- slug (UID) - Required
- content (Rich Text)
- difficulty (Enumeration: Easy, Medium, Hard)
- duration (Text)
- steps (Component - Repeatable)
- images (Media - Multiple)
- destinations (Relation - Many to Many with Destinations)
- published (Boolean)
```

### 3. Create Components

#### SEO Component
```javascript
Fields:
- metaTitle (Text)
- metaDescription (Text)
- keywords (Text)
- ogImage (Media - Single)
```

#### Guide Steps Component
```javascript
Fields:
- stepNumber (Number)
- title (Text)
- description (Rich Text)
- image (Media - Single)
- tips (Text - Multiple)
```

### 4. Sample Content Categories
```javascript
// Create these categories in Strapi:
1. Destinations
2. Travel Guides  
3. Culture & History
4. Food & Drink
5. Adventure
6. City Guides
7. Hidden Gems
8. Travel Tips
```

## 🔧 Flutter Integration

### 1. Install Dependencies
```bash
cd travel_app
flutter pub get
```

### 2. Initialize Firebase
```dart
// Add to main.dart
import 'package:firebase_core/firebase_core.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(const UpAlbaniaApp());
}
```

### 3. Configure Strapi Service
```dart
// Update lib/services/strapi_service.dart
class StrapiService {
  static const String baseUrl = 'https://your-strapi-instance.herokuapp.com/api';
  // Replace with your actual Strapi URL
}
```

### 4. Usage Examples

#### User Authentication
```dart
final firebaseService = FirebaseService();

// Sign up
await firebaseService.signUpWithEmail(
  '<EMAIL>', 
  'password123', 
  'John Doe'
);

// Sign in
await firebaseService.signInWithEmail(
  '<EMAIL>', 
  'password123'
);
```

#### Fetch CMS Content
```dart
// Get latest posts
final posts = await StrapiService.fetchLatestPosts(limit: 10);

// Get destinations
final destinations = await StrapiService.fetchDestinations();

// Search content
final searchResults = await StrapiService.searchPosts('Saranda');
```

#### Save User Data
```dart
// Create booking
await firebaseService.createBooking({
  'itineraryId': 'itinerary_123',
  'itineraryTitle': 'Albanian Riviera Explorer',
  'startDate': DateTime.now(),
  'endDate': DateTime.now().add(Duration(days: 7)),
  'totalPrice': 899.0,
  'travelers': 2,
});

// Add to favorites
await firebaseService.addToFavorites(
  userId, 
  'destination_123', 
  'destination'
);
```

## 🚀 Deployment

### 1. Strapi Deployment (Heroku)
```bash
# Install Heroku CLI
# Login to Heroku
heroku login

# Create app
heroku create upalb-cms

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Deploy
git push heroku main
```

### 2. Firebase Security Rules
```javascript
// Firestore Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read/write their own bookings
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Anyone can read published posts
    match /posts/{postId} {
      allow read: if resource.data.published == true;
    }
    
    // Users can read/write their own favorites and reviews
    match /favorites/{favoriteId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    match /reviews/{reviewId} {
      allow read: if true;
      allow write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
  }
}
```

## 📱 Testing

### 1. Run the App
```bash
flutter run -d chrome
```

### 2. Test Features
- [ ] User registration/login
- [ ] Browse destinations
- [ ] Read blog posts
- [ ] Create bookings
- [ ] Add favorites
- [ ] Write reviews
- [ ] Sync CMS content

## 🔄 Content Workflow

### 1. Content Creation (Strapi)
1. Login to Strapi admin panel
2. Create new destination/post
3. Add images, content, metadata
4. Set to published
5. Content automatically syncs to Firebase

### 2. App Updates
1. Content appears in app immediately
2. Users can interact with new content
3. All interactions saved to Firebase
4. Analytics tracked automatically

## 🎯 Next Steps

1. **Setup Firebase project** and add configuration files
2. **Deploy Strapi CMS** to Heroku or your preferred hosting
3. **Create sample content** in Strapi admin panel
4. **Test the complete flow** from CMS to mobile app
5. **Configure push notifications** for new content
6. **Add analytics** to track user behavior
7. **Implement offline caching** for better performance

## 📞 Support

For setup assistance or questions:
- Check Firebase documentation
- Review Strapi documentation  
- Test with sample data first
- Monitor console logs for errors

Your UpAlbania app is now ready for a complete travel experience! 🇦🇱✨
