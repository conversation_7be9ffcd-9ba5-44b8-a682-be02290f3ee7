{"name": "upalb-cms", "private": true, "version": "0.1.0", "description": "UpAlbania Travel CMS - Strapi application for managing Albanian travel content", "scripts": {"build": "strapi build", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "seed": "node scripts/seed.js"}, "devDependencies": {}, "dependencies": {"@strapi/strapi": "4.15.5", "@strapi/plugin-users-permissions": "4.15.5", "@strapi/plugin-i18n": "4.15.5", "@strapi/plugin-seo": "^1.9.7", "@strapi/plugin-upload": "4.15.5", "@strapi/plugin-email": "4.15.5", "better-sqlite3": "8.6.0", "pg": "^8.11.3", "pg-connection-string": "^2.6.2"}, "author": {"name": "UpAlbania Team"}, "strapi": {"uuid": "upalb-travel-cms"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT"}