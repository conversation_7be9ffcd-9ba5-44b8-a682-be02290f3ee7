import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'firebase_service.dart';
import 'database_service.dart';
import 'strapi_service.dart';
import '../models/user_model.dart';
import '../models/cms_model.dart';

class HybridSyncService {
  static final HybridSyncService _instance = HybridSyncService._internal();
  factory HybridSyncService() => _instance;
  HybridSyncService._internal();

  final FirebaseService _firebaseService = FirebaseService();
  final DatabaseService _databaseService = DatabaseService();
  final StrapiService _strapiService = StrapiService();
  final Connectivity _connectivity = Connectivity();

  bool _isOnline = true;
  Timer? _syncTimer;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  // Initialize the hybrid sync service
  Future<void> initialize() async {
    // Check initial connectivity
    final connectivityResult = await _connectivity.checkConnectivity();
    _isOnline = connectivityResult != ConnectivityResult.none;

    // Listen to connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (ConnectivityResult result) {
        final wasOnline = _isOnline;
        _isOnline = result != ConnectivityResult.none;
        
        if (!wasOnline && _isOnline) {
          // Just came online, sync pending data
          _syncPendingData();
        }
      },
    );

    // Start periodic sync (every 5 minutes when online)
    _startPeriodicSync();

    // Initial content sync
    if (_isOnline) {
      await syncContentFromCMS();
    }
  }

  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_isOnline) {
        _syncPendingData();
      }
    });
  }

  // User data operations (Firebase + Local cache)
  Future<UserModel?> getUser(String userId) async {
    if (_isOnline) {
      try {
        // Try to get from Firebase first
        final userData = await _firebaseService.getUserProfile(userId);
        if (userData != null) {
          final user = UserModel.fromMap(userId, userData);
          // Cache locally
          await _databaseService.cacheUser(user);
          return user;
        }
      } catch (e) {
        print('Error fetching user from Firebase: $e');
      }
    }
    
    // Fallback to local cache
    return await _databaseService.getCachedUser(userId);
  }

  Future<void> updateUser(String userId, Map<String, dynamic> updates) async {
    if (_isOnline) {
      try {
        await _firebaseService.updateUserProfile(userId, updates);
        // Update local cache
        final cachedUser = await _databaseService.getCachedUser(userId);
        if (cachedUser != null) {
          // Update cached user with new data
          await _databaseService.cacheUser(cachedUser);
        }
      } catch (e) {
        print('Error updating user in Firebase: $e');
        // Add to sync queue for later
        await _addToSyncQueue('users', userId, 'update', updates);
      }
    } else {
      // Offline: add to sync queue
      await _addToSyncQueue('users', userId, 'update', updates);
    }
  }

  // Booking operations (Firebase + Local)
  Future<String> createBooking(Map<String, dynamic> bookingData) async {
    // Always save locally first
    final bookingId = await _databaseService.createBooking(bookingData);
    
    if (_isOnline) {
      try {
        // Try to sync to Firebase
        final firebaseId = await _firebaseService.createBooking(bookingData);
        if (firebaseId != null) {
          // Update local record with Firebase ID
          // Mark as synced
        }
      } catch (e) {
        print('Error creating booking in Firebase: $e');
        // Will be synced later when online
      }
    }
    
    return bookingId;
  }

  Future<List<BookingModel>> getUserBookings(String userId) async {
    List<BookingModel> bookings = [];
    
    if (_isOnline) {
      try {
        // Get from Firebase
        final firebaseBookings = await _firebaseService.getUserBookings(userId);
        // Convert and cache locally
        // bookings = firebaseBookings.docs.map((doc) => BookingModel.fromFirestore(doc)).toList();
      } catch (e) {
        print('Error fetching bookings from Firebase: $e');
      }
    }
    
    // Always include local bookings (for offline-created ones)
    final localBookings = await _databaseService.getUserBookings(userId);
    // Merge and deduplicate bookings
    
    return bookings;
  }

  // Favorites operations (Firebase + Local)
  Future<void> addToFavorites(String userId, String itemId, String itemType, {
    String? itemTitle,
    String? itemImage,
  }) async {
    // Save locally first
    await _databaseService.addToFavorites(
      userId, 
      itemId, 
      itemType,
      itemTitle: itemTitle,
      itemImage: itemImage,
    );
    
    if (_isOnline) {
      try {
        await _firebaseService.addToFavorites(userId, itemId, itemType);
      } catch (e) {
        print('Error adding favorite to Firebase: $e');
        // Will be synced later
      }
    }
  }

  Future<void> removeFromFavorites(String userId, String itemId, String itemType) async {
    // Remove locally first
    await _databaseService.removeFromFavorites(userId, itemId, itemType);
    
    if (_isOnline) {
      try {
        await _firebaseService.removeFromFavorites(userId, itemId);
      } catch (e) {
        print('Error removing favorite from Firebase: $e');
        // Will be synced later
      }
    }
  }

  Future<List<Map<String, dynamic>>> getUserFavorites(String userId, String itemType) async {
    if (_isOnline) {
      try {
        // Sync from Firebase first
        // Then return local cache
      } catch (e) {
        print('Error syncing favorites from Firebase: $e');
      }
    }
    
    return await _databaseService.getUserFavorites(userId, itemType);
  }

  // Content operations (Strapi CMS + Local cache)
  Future<List<DestinationPost>> getDestinations({
    String? region,
    String? category,
    int limit = 20,
  }) async {
    if (_isOnline) {
      try {
        // Fetch fresh data from Strapi
        print('🌐 Fetching destinations from Strapi...');
        final strapiDestinations = await StrapiService.getDestinations();

        if (strapiDestinations.isNotEmpty) {
          print('✅ Loaded ${strapiDestinations.length} destinations from Strapi');

          // Convert DestinationModel to DestinationPost
          final destinations = strapiDestinations.map((dest) => DestinationPost(
            id: dest.id.toString(),
            title: dest.name,
            slug: dest.slug,
            excerpt: dest.shortDescription ?? dest.description.substring(0, dest.description.length > 150 ? 150 : dest.description.length),
            content: dest.description,
            featuredImage: dest.featuredImage ?? '',
            gallery: dest.gallery ?? [],
            category: dest.category,
            tags: dest.tags ?? [],
            author: 'UpAlbania',
            authorImage: '',
            published: true,
            publishedAt: dest.createdAt,
            createdAt: dest.createdAt,
            updatedAt: dest.updatedAt,
            seo: {},
            metadata: {
              'rating': dest.rating,
              'reviewCount': dest.reviewCount,
              'entryFee': dest.entryFee,
              'currency': dest.currency,
              'difficulty': dest.difficulty,
              'estimatedDuration': dest.estimatedDuration,
              'isPopular': dest.isPopular,
              'isFeatured': dest.isFeatured,
            },
            latitude: dest.latitude ?? 41.3275,
            longitude: dest.longitude ?? 19.8187,
            country: 'Albania',
            region: dest.region,
            highlights: dest.tags ?? [],
            weather: {'info': dest.weatherInfo ?? ''},
            transportation: {'info': dest.transportationInfo ?? ''},
          )).toList();

          // Cache locally (skip on web)
          await _databaseService.cacheDestinations(destinations);
          return destinations;
        }
      } catch (e) {
        print('❌ Error fetching destinations from Strapi: $e');
      }
    }
    
    // Fallback to local cache
    final cachedData = await _databaseService.getCachedDestinations(
      region: region,
      category: category,
      limit: limit,
    );
    
    return cachedData.map((data) => _mapToDestinationPost(data)).toList();
  }

  Future<List<CMSPost>> getPosts({
    String? category,
    int limit = 10,
  }) async {
    if (_isOnline) {
      try {
        // Fetch fresh data from Strapi
        // TODO: Update to use new Strapi service methods
        final posts = <CMSPost>[];
        
        if (posts.isNotEmpty) {
          // Cache locally
          await _databaseService.cachePosts(posts);
          return posts;
        }
      } catch (e) {
        print('Error fetching posts from Strapi: $e');
      }
    }
    
    // Fallback to local cache
    final cachedData = await _databaseService.getCachedPosts(
      category: category,
      limit: limit,
    );
    
    return cachedData.map((data) => _mapToCMSPost(data)).toList();
  }

  Future<CMSPost?> getPostBySlug(String slug) async {
    if (_isOnline) {
      try {
        // TODO: Update to use new Strapi service methods
        final post = null;
        if (post != null) {
          // Cache locally
          await _databaseService.cachePosts([post]);
          return post;
        }
      } catch (e) {
        print('Error fetching post by slug from Strapi: $e');
      }
    }
    
    // Try local cache
    final db = await _databaseService.database;
    if (db == null) {
      // On web platform, database is not available, return null for now
      // TODO: Implement direct API call for posts by slug
      return null;
    }

    final maps = await db.query(
      'posts',
      where: 'slug = ?',
      whereArgs: [slug],
    );
    
    if (maps.isNotEmpty) {
      return _mapToCMSPost(maps.first);
    }
    
    return null;
  }

  // Search operations (Local cache for offline support)
  Future<List<Map<String, dynamic>>> searchContent(String query) async {
    // Always search local cache for instant results
    final localResults = await _databaseService.searchContent(query);
    
    if (_isOnline) {
      try {
        // Also search Strapi for fresh results
        // TODO: Update to use new Strapi service methods
        final strapiResults = <CMSPost>[];
        // Merge results and cache new ones
        if (strapiResults.isNotEmpty) {
          await _databaseService.cachePosts(strapiResults);
        }
      } catch (e) {
        print('Error searching Strapi: $e');
      }
    }
    
    return localResults;
  }

  // Content sync from CMS
  Future<void> syncContentFromCMS() async {
    if (!_isOnline) return;
    
    try {
      print('Syncing content from CMS...');
      
      // Sync destinations
      // TODO: Update to use new Strapi service methods
      final destinations = <DestinationPost>[];
      if (destinations.isNotEmpty) {
        await _databaseService.cacheDestinations(destinations);
        print('Synced ${destinations.length} destinations');
      }
      
      // Sync posts
      // TODO: Update to use new Strapi service methods
      final posts = <CMSPost>[];
      if (posts.isNotEmpty) {
        await _databaseService.cachePosts(posts);
        print('Synced ${posts.length} posts');
      }
      
      print('Content sync completed');
    } catch (e) {
      print('Error syncing content from CMS: $e');
    }
  }

  // Sync pending data to Firebase
  Future<void> _syncPendingData() async {
    if (!_isOnline) return;
    
    try {
      final pendingItems = await _databaseService.getPendingSyncItems();
      
      for (final item in pendingItems) {
        try {
          final tableName = item['table_name'] as String;
          final recordId = item['record_id'] as String;
          final action = item['action'] as String;
          final data = jsonDecode(item['data'] as String);
          
          bool success = false;
          
          switch (tableName) {
            case 'bookings':
              if (action == 'create') {
                final firebaseId = await _firebaseService.createBooking(data);
                success = firebaseId != null;
              }
              break;
              
            case 'favorites':
              if (action == 'create') {
                await _firebaseService.addToFavorites(
                  data['user_id'],
                  data['item_id'],
                  data['item_type'],
                );
                success = true;
              } else if (action == 'delete') {
                await _firebaseService.removeFromFavorites(
                  data['user_id'],
                  data['item_id'],
                );
                success = true;
              }
              break;
              
            case 'reviews':
              if (action == 'create') {
                await _firebaseService.addReview(data);
                success = true;
              }
              break;
          }
          
          if (success) {
            await _databaseService.markSyncComplete(item['id'] as int);
          } else {
            await _databaseService.incrementSyncRetry(item['id'] as int);
          }
        } catch (e) {
          print('Error syncing item ${item['id']}: $e');
          await _databaseService.incrementSyncRetry(item['id'] as int);
        }
      }
      
      if (pendingItems.isNotEmpty) {
        print('Synced ${pendingItems.length} pending items');
      }
    } catch (e) {
      print('Error syncing pending data: $e');
    }
  }

  // Helper methods
  Future<void> _addToSyncQueue(String tableName, String recordId, String action, Map<String, dynamic> data) async {
    final db = await _databaseService.database;
    if (db == null) return; // Skip on web platform

    await db.insert('sync_queue', {
      'table_name': tableName,
      'record_id': recordId,
      'action': action,
      'data': jsonEncode(data),
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'retry_count': 0,
    });
  }

  DestinationPost _mapToDestinationPost(Map<String, dynamic> data) {
    return DestinationPost(
      id: data['id'],
      title: data['title'],
      slug: data['slug'],
      excerpt: data['excerpt'] ?? '',
      content: data['description'] ?? '',
      featuredImage: data['featured_image'] ?? '',
      gallery: List<String>.from(jsonDecode(data['gallery'] ?? '[]')),
      category: data['category'] ?? '',
      tags: [],
      author: '',
      authorImage: '',
      published: data['published'] == 1,
      publishedAt: DateTime.fromMillisecondsSinceEpoch(data['created_at']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(data['updated_at']),
      seo: {},
      metadata: {},
      latitude: data['latitude'] ?? 0.0,
      longitude: data['longitude'] ?? 0.0,
      country: data['country'] ?? 'Albania',
      region: data['region'] ?? '',
      highlights: List<String>.from(jsonDecode(data['highlights'] ?? '[]')),
      weather: {},
      transportation: {},
    );
  }

  CMSPost _mapToCMSPost(Map<String, dynamic> data) {
    return CMSPost(
      id: data['id'],
      title: data['title'],
      slug: data['slug'],
      excerpt: data['excerpt'] ?? '',
      content: data['content'] ?? '',
      featuredImage: data['featured_image'] ?? '',
      gallery: List<String>.from(jsonDecode(data['gallery'] ?? '[]')),
      category: data['category'] ?? '',
      tags: List<String>.from(jsonDecode(data['tags'] ?? '[]')),
      author: data['author'] ?? '',
      authorImage: data['author_image'] ?? '',
      published: data['published'] == 1,
      publishedAt: DateTime.fromMillisecondsSinceEpoch(data['published_at']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(data['updated_at']),
      seo: {},
      metadata: {},
    );
  }

  // Cleanup
  void dispose() {
    _syncTimer?.cancel();
    _connectivitySubscription?.cancel();
  }
}


