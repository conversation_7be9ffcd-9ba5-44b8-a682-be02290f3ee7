rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users can read/write their own profile data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow reading basic profile info for reviews/bookings
      allow read: if request.auth != null && 
        resource.data.keys().hasOnly(['name', 'avatar', 'created_at']);
    }
    
    // User bookings - private to each user
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.user_id;
    }
    
    // User favorites - private to each user
    match /favorites/{favoriteId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.user_id;
    }
    
    // Reviews - users can create, read all, edit/delete their own
    match /reviews/{reviewId} {
      allow read: if true; // Public reading
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.user_id;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
    }
    
    // Travel history - private to each user
    match /travel_history/{historyId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.user_id;
    }
    
    // CMS content sync - read-only for users, write for admin
    match /cms_posts/{postId} {
      allow read: if true; // Public reading
      allow write: if request.auth != null && 
        request.auth.token.admin == true; // Admin only
    }
    
    match /cms_destinations/{destinationId} {
      allow read: if true; // Public reading
      allow write: if request.auth != null && 
        request.auth.token.admin == true; // Admin only
    }
    
    // Analytics data - write-only for users
    match /analytics/{analyticsId} {
      allow create: if request.auth != null;
      allow read, update, delete: if request.auth != null && 
        request.auth.token.admin == true; // Admin only
    }
    
    // App configuration - read-only for all
    match /app_config/{configId} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.token.admin == true; // Admin only
    }
  }
}
