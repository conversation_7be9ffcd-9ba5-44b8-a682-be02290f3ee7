{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": "Content categories for organization"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "color": {"type": "string", "default": "#00CFC8"}, "icon": {"type": "string"}, "image": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "destinations": {"type": "relation", "relation": "oneToMany", "target": "api::destination.destination", "mappedBy": "category"}, "blogPosts": {"type": "relation", "relation": "oneToMany", "target": "api::blog-post.blog-post", "mappedBy": "category"}, "hotels": {"type": "relation", "relation": "oneToMany", "target": "api::hotel.hotel", "mappedBy": "category"}, "restaurants": {"type": "relation", "relation": "oneToMany", "target": "api::restaurant.restaurant", "mappedBy": "category"}, "order": {"type": "integer", "default": 1}, "featured": {"type": "boolean", "default": false}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}}}