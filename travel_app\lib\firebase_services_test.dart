import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialized successfully!');
    
    runApp(const FirebaseServicesTestApp());
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
    runApp(const ErrorApp());
  }
}

class FirebaseServicesTestApp extends StatelessWidget {
  const FirebaseServicesTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'UpAlbania Firebase Services Test',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF00CFC8),
          brightness: Brightness.light,
        ),
      ),
      home: const ServicesTestScreen(),
    );
  }
}

class ServicesTestScreen extends StatefulWidget {
  const ServicesTestScreen({super.key});

  @override
  State<ServicesTestScreen> createState() => _ServicesTestScreenState();
}

class _ServicesTestScreenState extends State<ServicesTestScreen> {
  String authStatus = 'Testing...';
  String firestoreStatus = 'Testing...';
  String storageStatus = 'Testing...';

  @override
  void initState() {
    super.initState();
    _testServices();
  }

  Future<void> _testServices() async {
    // Test Authentication
    try {
      final auth = FirebaseAuth.instance;
      print('✅ Firebase Auth instance created');
      setState(() {
        authStatus = '✅ Authentication Ready';
      });
    } catch (e) {
      print('❌ Firebase Auth error: $e');
      setState(() {
        authStatus = '❌ Authentication Error: $e';
      });
    }

    // Test Firestore
    try {
      final firestore = FirebaseFirestore.instance;
      
      // Try to access Firestore settings (this will fail if Firestore is not enabled)
      await firestore.enableNetwork();
      print('✅ Firestore connection successful');
      
      setState(() {
        firestoreStatus = '✅ Firestore Database Ready';
      });
    } catch (e) {
      print('❌ Firestore error: $e');
      setState(() {
        if (e.toString().contains('not found') || e.toString().contains('does not exist')) {
          firestoreStatus = '⚠️ Firestore Database Not Enabled\nPlease enable in Firebase Console';
        } else {
          firestoreStatus = '❌ Firestore Error: $e';
        }
      });
    }

    // Test Storage (we'll add this later)
    setState(() {
      storageStatus = '⏳ Storage test pending';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UpAlbania Firebase Services'),
        backgroundColor: const Color(0xFF00CFC8),
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF00CFC8),
              Color(0xFF00A9F6),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildServiceCard(
                'Firebase Authentication',
                authStatus,
                Icons.security,
              ),
              const SizedBox(height: 16),
              _buildServiceCard(
                'Firestore Database',
                firestoreStatus,
                Icons.storage,
              ),
              const SizedBox(height: 16),
              _buildServiceCard(
                'Firebase Storage',
                storageStatus,
                Icons.cloud_upload,
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    const Text(
                      '🔧 Setup Instructions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF00CFC8),
                      ),
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '1. Go to Firebase Console\n'
                      '2. Select your project: upalb-travel-app\n'
                      '3. Enable Authentication (Email/Password)\n'
                      '4. Enable Firestore Database (Test mode)\n'
                      '5. Enable Storage (Test mode)',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        _testServices();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF00CFC8),
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Retest Services'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceCard(String title, String status, IconData icon) {
    Color statusColor = Colors.orange;
    if (status.startsWith('✅')) {
      statusColor = Colors.green;
    } else if (status.startsWith('❌')) {
      statusColor = Colors.red;
    } else if (status.startsWith('⚠️')) {
      statusColor = Colors.orange;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 32,
            color: statusColor,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 14,
                    color: statusColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ErrorApp extends StatelessWidget {
  const ErrorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Firebase Error',
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Firebase Error'),
          backgroundColor: Colors.red,
        ),
        body: const Center(
          child: Text('Firebase initialization failed'),
        ),
      ),
    );
  }
}
