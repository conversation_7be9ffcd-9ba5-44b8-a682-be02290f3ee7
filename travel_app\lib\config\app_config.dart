class AppConfig {
  // App Information
  static const String appName = 'UpAlbania';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // Package Information
  static const String packageName = 'com.upalb.travel';
  static const String bundleId = 'com.upalb.travel';
  
  // API Configuration
  static const String strapiBaseUrl = 'https://your-strapi-instance.herokuapp.com/api';
  static const String strapiMediaUrl = 'https://your-strapi-instance.herokuapp.com';
  
  // Firebase Configuration (will be loaded from google-services.json)
  static const String firebaseProjectId = 'upalb-travel-app';
  static const String firebaseStorageBucket = 'upalb-travel-app.firebasestorage.app';
  
  // App Colors (UpAlbania Brand)
  static const int primaryTeal = 0xFF00CFC8;
  static const int primaryYellow = 0xFFFADC36;
  static const int primaryOrange = 0xFFFBB73F;
  static const int primaryBlue = 0xFF00A9F6;
  static const int primaryGreen = 0xFF00F69C;
  
  // Feature Flags
  static const bool enableOfflineMode = true;
  static const bool enablePushNotifications = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  
  // Cache Configuration
  static const int maxCacheSize = 100; // MB
  static const int cacheExpiryDays = 7;
  static const int syncIntervalMinutes = 5;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Map Configuration
  static const double defaultLatitude = 41.3275; // Albania center
  static const double defaultLongitude = 19.8187;
  static const double defaultZoom = 7.0;
  
  // Booking Configuration
  static const int maxTravelers = 20;
  static const int minAdvanceBookingDays = 1;
  static const int maxAdvanceBookingDays = 365;
  
  // File Upload Configuration
  static const int maxImageSizeMB = 10;
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Social Media Links
  static const String instagramUrl = 'https://instagram.com/upalb_travel';
  static const String facebookUrl = 'https://facebook.com/upalb.travel';
  static const String twitterUrl = 'https://twitter.com/upalb_travel';
  static const String websiteUrl = 'https://upalb.com';
  
  // Contact Information
  static const String supportEmail = '<EMAIL>';
  static const String businessEmail = '<EMAIL>';
  static const String phoneNumber = '+355 69 123 4567';
  
  // Legal
  static const String privacyPolicyUrl = 'https://upalb.com/privacy';
  static const String termsOfServiceUrl = 'https://upalb.com/terms';
  
  // Environment-specific configurations
  static bool get isProduction => const bool.fromEnvironment('dart.vm.product');
  static bool get isDevelopment => !isProduction;
  
  // Debug settings
  static bool get enableDebugLogs => isDevelopment;
  static bool get enableNetworkLogs => isDevelopment;
  
  // Get environment-specific Strapi URL
  static String get strapiUrl {
    if (isProduction) {
      return 'https://cms.upalb.com/api';
    } else {
      return 'http://localhost:1337/api';
    }
  }
  
  // Get environment-specific Strapi media URL
  static String get strapiMedia {
    if (isProduction) {
      return 'https://cms.upalb.com';
    } else {
      return 'http://localhost:1337';
    }
  }
}
